# SonaMesh 配置文件範例
# 此文件展示了如何正確配置 TLS 設置以確保安全性

server:
  address: ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

logger:
  level: "all"
  stdout: true

# WebSocket 配置
websocket:
  hand_shake_timeout: "45s"

# 廠商配置
vendor:
  # III Bot 配置
  iii:
    STT:
      url: "wss://your-iii-stt-server.com/ws"
      http_url: "https://your-iii-stt-server.com/api"
      # TLS 安全配置
      tls:
        # 是否跳過證書驗證（生產環境建議設為 false）
        insecure_skip_verify: false
        # TLS 最小版本（1.0, 1.1, 1.2, 1.3）
        min_version: "1.2"
    TTS:
      url: "https://your-iii-tts-server.com/api"
      # TLS 安全配置
      tls:
        insecure_skip_verify: false
        min_version: "1.2"

  # Cyberon 配置
  Cyberon_default:
    STT:
      ws:
        url: "wss://your-cyberon-stt-server.com/ws"
      # TLS 安全配置
      tls:
        insecure_skip_verify: false
        min_version: "1.2"
    TTS:
      http:
        url: "your-cyberon-tts-server.com:443"
      # TLS 安全配置
      tls:
        insecure_skip_verify: false
        min_version: "1.2"

  # EmotiBot 配置
  EmotiBot_default:
    STT:
      ws:
        url: "wss://your-emotibot-stt-server.com/ws"
      http:
        url: "https://your-emotibot-stt-server.com/api"
      # TLS 安全配置
      tls:
        insecure_skip_verify: false
        min_version: "1.2"
    TTS:
      http:
        url: "https://your-emotibot-tts-server.com/api"
      # TLS 安全配置
      tls:
        insecure_skip_verify: false
        min_version: "1.2"

# 開發環境配置範例（僅供開發使用）
# 如果您在開發環境中使用自簽名證書或內部 CA，可以這樣配置：
development:
  vendor:
    iii:
      STT:
        tls:
          # 開發環境可以暫時跳過證書驗證
          insecure_skip_verify: true
          min_version: "1.2"
    Cyberon_default:
      TTS:
        tls:
          insecure_skip_verify: true
          min_version: "1.2"

# 安全建議：
# 1. 生產環境中 insecure_skip_verify 應該設為 false
# 2. 使用有效的 SSL/TLS 證書
# 3. 定期更新證書
# 4. 使用 TLS 1.2 或更高版本
# 5. 如果必須使用自簽名證書，考慮配置 CA 證書路徑
