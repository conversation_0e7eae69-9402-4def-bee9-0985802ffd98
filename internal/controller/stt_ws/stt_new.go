package stt_ws

import (
	"SonaMesh/api/stt_ws"
	"SonaMesh/internal/consts"
	"SonaMesh/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type ControllerV1 struct {
}

func (c *ControllerV1) WSProc(r *ghttp.Request) {

	ctx := r.GetCtx()
	ws, err := r.WebSocket()

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		r.Exit()
	}

	err = service.WebSocketMan().WebSocketReq(ctx, ws)

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		r.Exit()
	}

}

func NewV1() stt_ws.ISTT {
	return &ControllerV1{}
}
