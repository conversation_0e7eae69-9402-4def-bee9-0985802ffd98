package tts

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gogf/gf/v2/encoding/gbase64"

	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"

	v1 "SonaMesh/api/tts/v1"
	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/internal/service"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

func (c *ControllerV1) DownloadVoiceFile(ctx context.Context, req *v1.DownloadVoiceFileReq) (res *v1.DownloadVoiceFileRes, err error) {
	g.Log().Cat(INFO).Infof(ctx, "DownloadVoiceFile: %v", gjson.New(req).MustToJsonIndentString())
	r := g.RequestFromCtx(ctx)
	saveFlag, _ := g.Cfg().Get(ctx, "vendor.save_voice_file", false)
	if saveFlag.Bool() {
		filePath, _ := g.Cfg().Get(ctx, "vendor.save_folder", "./voc")
		ti := gtime.NewFromStr(gfile.Name(req.FileName))
		if ti != nil {
			fullName := gfile.Join(filePath.String(), ti.Format("Y-m-d"), req.FileName)
			if gfile.Exists(fullName) {
				r.Response.ServeFileDownload(fullName)
			} else {
				r.Response.WriteStatus(http.StatusNotFound)
			}
		}
	}
	return
}

func (c *ControllerV1) Synthesis(ctx context.Context, req *v1.TtsReq) (res *v1.TtsRes, err error) {
	g.Log().Cat(INFO).Infof(ctx, "Synthesis: %v", gjson.New(req).MustToJsonIndentString())
	r := g.RequestFromCtx(ctx)
	var result *model.TtsRes

	result, err = service.Tts().Synthesis(ctx, &model.TtsReq{
		Text:            c.decodeString(ctx, req.Text),
		Language:        req.Language,
		Type:            req.Type,
		Speed:           req.Speed,
		Volume:          req.Volume,
		UserId:          req.UserId,
		VccId:           req.VccId,
		RouteAccessCode: req.RouteAccessCode,
		TextFormat:      req.TextFormat,
	})

	if err != nil {
		r.Response.WriteStatus(http.StatusBadRequest, g.Map{
			"code":    gerror.Code(err).Code(),
			"message": gerror.Code(err).Message(),
		})
	} else {

		r.Response.Header().Set("Content-Type", fmt.Sprintf(`audio/%s`, TypeSuffix[gstr.ToLower(result.Ext)]))
		r.Response.Header().Set("VocFileName", result.FileName)
		r.Response.WriteStatus(http.StatusOK, result.Buf)
		result.Buf = make([]byte, 0)

	}

	return
}

// decodeString decodes the given content string based on the value of the "vendor.TTS.EnableDecode" configuration.
// If the "EnableDecode" configuration is set to true, the content string is decoded using base64 decoding and returned.
// Otherwise, the original content string is returned as it is.
func (c *ControllerV1) decodeString(_ context.Context, content string) string {
	if gstr.HasPrefix(content, TTSPrefix) {
		tts := gstr.TrimLeftStr(content, TTSPrefix)
		return gbase64.MustDecodeToString(tts)
	}
	return content
}
