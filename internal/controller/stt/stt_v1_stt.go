package stt

import (
	v1 "SonaMesh/api/stt/v1"
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/internal/service"
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/util/gconv"
)

// RecognizeAudioFile is a method of the ControllerV1 struct that processes an audio file for speech-to-text recognition.
// It takes two input parameters:
//   - ctx: A context.Context for logging and error handling purposes.
//   - req: A pointer to a v1.SttReq struct containing the request data, including the audio file, VccId, and RouteAccessCode.
// The function returns two output parameters:
//   - res: A pointer to a v1.SttRes struct containing the recognition result, response code, and message.
//   - err: An error value indicating if any error occurred during the recognition process.
//
// The function first logs the incoming request and saves the audio file to a temporary directory.
// It initializes the response with a default OK status.
// The function then removes the temporary file in a deferred function call.
// If there is an error saving the file, it sets the response code and message to indicate a general error.
// Otherwise, it calls the RecognizeFile method of the STT service with the appropriate parameters.
// If there is an error during recognition, it sets the response code and message based on the error.
// Finally, it writes the JSON response to the client.

func (c *ControllerV1) RecognizeAudioFile(ctx context.Context, req *v1.SttReq) (res *v1.SttRes, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Recognize audio file:%v", gjson.New(req).MustToJsonIndentString())

	fileName, err := req.File.Save(gfile.Temp("sonamesh"))
	res = &v1.SttRes{
		Code:    consts.ErrorOK.Code(),
		Message: consts.ErrorOK.Message(),
	}

	defer func() {
		if gfile.Exists(gfile.Join(gfile.Temp("sonamesh"), fileName)) {
			_ = gfile.Remove(gfile.Join(gfile.Temp("sonamesh"), fileName))
		}
	}()

	if err != nil {
		res.Code = consts.ErrorGeneralError.Code()
		res.Message = consts.ErrorGeneralError.Message()
	} else {
		var result any
		result, err = service.STT().RecognizeFile(
			ctx,
			&model.STTParams{
				Vccid:           req.VccId,
				RouteAccessCode: req.RouteAccessCode,
				FileName:        gfile.Join(gfile.Temp("sonamesh"), fileName),
				WebHook:         req.WebHook,
				WaitResult:      req.IsWaitResult,
				ResKey:          req.ResourceKey,
				EndPointID:      req.EndPointID,
				Region:          req.Region,
			},
		)
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()

		} else {
			res.Result = gconv.String(result)
		}

	}
	g.RequestFromCtx(ctx).Response.WriteJson(res)

	return

}
