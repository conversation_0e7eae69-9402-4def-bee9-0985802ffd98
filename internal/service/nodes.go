package service

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"context"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/mroth/weightedrand/v2"
	"sync"
)

var (
	mu                     = sync.Mutex{}
	azureProfileKeyToNodes = gmap.NewStrAnyMap(true)
)

func init() {
	ctx := gctx.GetInitCtx()
	if err := readAzureNodes(ctx); err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
	}

}

// SetNodeCost adjusts the cost of a node in a dynamic weight selector, recalculating its weight after the update.
// It locks to prevent concurrent map writes, retrieves the selection mode, and updates node cost if applicable.
func SetNodeCost(ctx context.Context, profileKey, key string, cost int) {
	mu.Lock()
	defer mu.Unlock()

	vSelector, err := g.Cfg().Get(ctx, "system.azure_endpoint_selector", "rand")
	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
		return
	}

	if gstr.ToLower(vSelector.String()) == consts.AZSelectorDynamicWeight {
		sKey := profileKey
		if !azureProfileKeyToNodes.Contains(sKey) {
			g.Log().Cat(consts.DEBUG).Debugf(ctx, "The key : %v  is not found ", profileKey)
			return
		}
		vNodes := azureProfileKeyToNodes.GetVar(sKey)
		if !vNodes.IsNil() {
			var nodesAry []*model.Node
			_ = vNodes.Scan(&nodesAry)
			if nodesAry == nil || len(nodesAry) == 0 {
				return
			}

			for _, node := range nodesAry {
				if node.Key == key {
					node.SetDuration(ctx, cost)
					node.RecaculateWeight()
					g.Log().Cat(consts.DEBUG).Debugf(ctx, "%v", gjson.New(node).MustToJsonIndentString())
					break
				}
			}

		}
	}

}

// GetAzureEndPointKeyAndRegion retrieves the Azure endpoint key and region based on a profile key and tag.
func GetAzureEndPointKeyAndRegion(ctx context.Context, profileKey string) (key string, region string) {
	key = profileKey
	if !azureProfileKeyToNodes.Contains(key) {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "The key : %v is not found ", profileKey)
		return
	}

	vSelector, err := g.Cfg().Get(ctx, "system.azure_endpoint_selector", "rand")
	vNodes := azureProfileKeyToNodes.GetVar(key)
	var nodesAry []*model.Node
	_ = vNodes.Scan(&nodesAry)
	if nodesAry == nil || len(nodesAry) == 0 {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "The nodes  : %v is not found ", profileKey)
		return
	}

	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
		return
	}
	switch gstr.ToLower(vSelector.String()) {
	default:
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Not supported selector : %v", vSelector.String())
	case consts.AZSelectorRand:
		indexOfNode := grand.N(0, len(nodesAry)-1)
		if indexOfNode < len(nodesAry) {
			key = nodesAry[indexOfNode].Key
			region = nodesAry[indexOfNode].Region
			return
		}

	case consts.AZSelectorWeightRand, consts.AZSelectorDynamicWeight:
		nodeWeightAry := make([]weightedrand.Choice[*model.Node, int], 0)

		for _, node := range nodesAry {
			nodeWeightAry = append(nodeWeightAry, weightedrand.Choice[*model.Node, int]{Item: node, Weight: node.Weight})
		}
		chooser, e := weightedrand.NewChooser(nodeWeightAry...)
		if e != nil {
			g.Log().Cat(consts.DEBUG).Debug(ctx, e)
			return
		}
		g.TryCatch(
			ctx,
			func(ctx context.Context) {
				result := chooser.Pick()
				if result != nil {
					key = result.Key
					region = result.Region
				}
			},
			func(ctx context.Context, exception error) {
				g.Log().Cat(consts.DEBUG).Debug(ctx, exception)

			},
		)

	}
	return
}

// readAzureNodes loads Azure STT and TTS credentials from configuration, configures their weights, and stores them.
func readAzureNodes(ctx context.Context) (err error) {
	v, err := g.Cfg().Get(ctx, "vendor")
	if err != nil {
		return
	}
	m := gmap.NewStrAnyMapFrom(v.MapStrAny())
	m.Iterator(func(k string, v interface{}) bool {
		if gstr.HasPrefix(k, "Azure") {
			jsParams := gjson.New(v)
			fnDynWeightDefaultSet := func(nodes []*model.Node) {
				vSelector, e := g.Cfg().Get(ctx, "system.azure_endpoint_selector", "rand")
				if e != nil {
					g.Log().Cat(consts.DEBUG).Debug(ctx, e)

				} else {
					if gstr.ToLower(vSelector.String()) == consts.AZSelectorDynamicWeight {
						for _, node := range nodes {
							node.Weight = 100
						}
					}
				}
			}

			if jsParams.Contains("STT") {
				// nodes for stt
				var nodes = make([]*model.Node, 0)
				_ = jsParams.Get("STT.credentials").Structs(&nodes)
				if len(nodes) == 0 {
					err = gerror.New("STT credential is missing")
					return false
				}

				fnDynWeightDefaultSet(nodes)
				azureProfileKeyToNodes.Set("vendor."+k+".STT", nodes)

			}

			if jsParams.Contains("TTS") {
				// nodes for tts
				var nodes = make([]*model.Node, 0)
				_ = jsParams.Get("TTS.credentials").Structs(&nodes)
				if len(nodes) == 0 {
					err = gerror.New("TTS credential is missing")
					return false
				}
				fnDynWeightDefaultSet(nodes)
				azureProfileKeyToNodes.Set("vendor."+k+".TTS", nodes)
			}

		}
		return true
	})

	return
}
