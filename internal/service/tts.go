// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"SonaMesh/internal/model"
	"context"
)

type (
	ITts interface {
		Synthesis(ctx context.Context, params *model.TtsReq) (res *model.TtsRes, err error)
	}
)

var (
	localTts ITts
)

func Tts() ITts {
	if localTts == nil {
		panic("implement not found for interface ITts, forgot register?")
	}
	return localTts
}

func RegisterTts(i ITts) {
	localTts = i
}
