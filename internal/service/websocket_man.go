// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IWebSocketMan interface {
		WebSocketReq(ctx context.Context, ws *ghttp.WebSocket) (err error)
	}
)

var (
	localWebSocketMan IWebSocketMan
)

func WebSocketMan() IWebSocketMan {
	if localWebSocketMan == nil {
		panic("implement not found for interface IWebSocketMan, forgot register?")
	}
	return localWebSocketMan
}

func RegisterWebSocketMan(i IWebSocketMan) {
	localWebSocketMan = i
}
