// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	ISTT interface {
		Start(ctx context.Context, params *model.SttStartReq, resultHandler consts.ResultFunc, finishHandler consts.AckFunc) (ack *model.SttActionAck, err error)
		SendBuffer(ctx context.Context, buf []byte) (err error)
		Stop(ctx context.Context, params *model.SttStopReq) (ack *model.SttActionAck, err error)
		RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error)
		Interrupt(ctx context.Context)
	}
)

var (
	localSTT ISTT
)

func STT(ws ...*ghttp.WebSocket) ISTT {

	if localSTT == nil {
		panic("implement not found for interface ISTT, forgot register?")
	}
	return localSTT
}

func RegisterSTT(i ISTT) {
	localSTT = i
}
