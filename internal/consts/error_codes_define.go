package consts

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

var (
	ErrorOK               = gcode.New(0, "success", nil)
	ErrorGeneralError     = gcode.New(-1, "System error", nil)
	ErrorFormatWrong      = gcode.New(-2, "The format of the data is not the standard json format", nil)
	ErrorRequestWrong     = gcode.New(-3, "Action parameter error", nil)
	ErrorReadMessageError = gcode.New(-4, "Read message error", nil)
	ErrorParamsError      = gcode.New(-5, "Parameter setting error", nil)
	ErrorState            = gcode.New(-6, "error state", nil)
	ErrorRecogFailed      = gcode.New(-7, "Recognition failed", nil)
)
