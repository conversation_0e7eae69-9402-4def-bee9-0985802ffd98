package consts

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

const (
	ERROR = `error`
	INFO  = `info`
	DEBUG = `debug`
	IR    = `interaction`
	EM    = `emotibot`
	AZ    = `azure`
	CY    = `cyberon`
	IQ    = `IQ`
	III   = `III`
)

const (
	ActionStart  = "start"
	ActionStop   = "stop"
	ActionFinish = "finish"
	ActionRead   = "read"
	ActionSend   = "send"
)

const (
	StateListening = `listening`
	StateResult    = `result`
	StateAck       = `ack`
	StateError     = "error"
	StateFinish    = "finish"
)

const (
	AckINIT = "INIT"
	AckStop = "EOS"
)

const (
	WavePCM   = "pcm"
	WaveALAW  = "alaw"
	WaveMULAW = "mulaw"
)
const (
	AZSelectorRand          = "rand"
	AZSelectorWeightRand    = "weight_rand"
	AZSelectorDynamicWeight = "dynamic_weight"
)

const (
	CfgKeyTTS         = "vendor.TTS"
	CfgKeySTT         = "vendor.STT"
	CfgKeyCyberonSTT  = `vendor.Cyberon_%v.STT`
	CfgKeyCyberonTTS  = `vendor.Cyberon_%v.TTS`
	CfgKeyEmotiBotSTT = `vendor.EmotiBot_%v.STT`
	CfgKeyEmotiBotTTS = `vendor.EmotiBot_%v.TTS`
	CfgKeyIIIBotTTS   = `vendor.III_%v.TTS`
	CfgKeyIIIBotSTT   = `vendor.III_%v.STT`
	CfgKeyIQTTS       = `vendor.IQ_%v.TTS`
	CfgKeyAzureTTS    = "vendor.Azure_%v.TTS"
	CfgKeyAzureSTT    = "vendor.Azure_%v.STT"
)

const (
	LanguagePlaceholder = "$language"
	SpeakerPlaceholder  = "$speaker"
	SpeedPlaceholder    = "$speed"
	VolumePlaceholder   = "$volume"
	PitchPlaceholder    = "$pitch"
)

// Vendor
const (
	VendorCyberon  = "Cyberon"
	VendorEmotiBot = "EmotiBot"
	VendorAzure    = "Azure"
	VendorIQ       = "IQ"
	VendorIII      = `III`
)

const (
	CyberonStopCommand       = `{"action":"stop"}`
	CyberonCancelCommand     = `{"action":"cancel"}`
	EmotiStopCommand         = `{"message":{"type":"EOS"}}`
	EmotiStartRecFileCommand = `SOS`
	EmotiStopRecFileCommand  = `EOS`
	RecordFile               = `records.json`
)

var CmdPattern = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "%s"`

var TypeSuffix = g.MapStrStr{
	"wav":      "wav",
	"mp3":      "mp3",
	"pcm(16K)": "pcm",
	"pcm8(8K)": "pcm",
	"pcm":      "pcm",
}

const TTSPrefix = `$PhoneConnector$:`
const KeyAzureProfile = "AzureKeyRegions"

type (
	AckFunc    func(ctx context.Context, data any)
	ResultFunc func(ctx context.Context, data any)
)
