package websocket_man

import (
	"SonaMesh/internal/consts"
	SttClient "SonaMesh/internal/logic/stt"
	"SonaMesh/internal/model"
	"SonaMesh/internal/service"
	"SonaMesh/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gorilla/websocket"
	"github.com/shirou/gopsutil/v4/process"
	"os"
	"sync"
)

var StarAckTimeoutCount = garray.NewIntArray(true)

type sWebSocketMan struct {
	workerPool *grpool.Pool // pool of goroutines
	params     *gmap.StrAnyMap
}

func init() {
	service.RegisterWebSocketMan(New())
}

func New() *sWebSocketMan {
	return &sWebSocketMan{
		workerPool: grpool.New(),
		params:     gmap.NewStrAnyMap(true),
	}
}

func (s *sWebSocketMan) WebSocketReq(ctx context.Context, ws *ghttp.WebSocket) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Client %s connected ...", ws.RemoteAddr().String())
	s.params.Set(
		gctx.CtxId(ctx),
		ws,
	)

	err = s.workerPool.AddWithRecover(
		ctx,
		s._processor,
		func(ctx context.Context, exception error) {
			g.Log().Cat(consts.ERROR).Error(ctx, exception)
		},
	)

	return
}

func (s *sWebSocketMan) _processor(ctx context.Context) {
	var ws *ghttp.WebSocket
	v := s.params.GetVar(gctx.CtxId(ctx))
	_ = v.Scan(&ws)
	if ws == nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, "the websocket object is nil ... ")
		return
	}
	mu := sync.Mutex{}
	mu.Lock()
	defer mu.Unlock()
	defer func() {
		s.params.Remove(gctx.CtxId(ctx))
	}()
	var err error
	defer func(ws *ghttp.WebSocket) {
		if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
			g.Log().Debug(ctx, "the connection closes gracefully")
		} else if websocket.IsCloseError(err, websocket.CloseGoingAway) {
			g.Log().Debug(ctx, "the connection is closed")
		} else if websocket.IsUnexpectedCloseError(err, websocket.CloseAbnormalClosure) {
			g.Log().Debug(ctx, "the connection is closed abnormally")
		} else {
			if ws != nil {
				g.Log().Cat(consts.DEBUG).Debug(ctx, "close websocket... ")
				e := ws.Close()
				if e != nil {
					g.Log().Error(ctx, e)
				}
			}

		}
		// add wave header
		if vSave, _ := g.Cfg().Get(ctx, "vendor.STT.save_voice_buffer", false); vSave.Bool() {
			vFolder, _ := g.Cfg().Get(ctx, "vendor.STT.save_folder")
			if vFolder.IsEmpty() {
				return
			}

			voiceFileName := gfile.Join(vFolder.String(), fmt.Sprintf("%s.wav", gctx.CtxId(ctx)))
			if !gfile.Exists(voiceFileName) {
				return
			}
			buf := gfile.GetBytes(voiceFileName)
			newBuf := utility.AddWAVHeader(buf, 16000, 1, 16)
			_ = gfile.Remove(voiceFileName)
			_ = gfile.PutBytes(voiceFileName, newBuf)

		}

	}(ws)

	if ws != nil {
		stt := SttClient.New()
		fnFailedClose := func(action string, e error) {
			g.Log().Cat(consts.ERROR).Error(ctx, e)
			ack := &model.SttActionAck{
				Message: struct {
					Type string `json:"type"`
				}(struct{ Type string }{"ack"}),
				Ack:    action,
				Status: gerror.Code(e).Code(),
			}

			_ = ws.WriteJSON(ack)
		}

		var (
			msgType int
			msg     []byte
		)

		for {

			msgType, msg, err = ws.ReadMessage()

			if err != nil {
				return
			}

			var act = ""
			switch msgType {
			case websocket.CloseMessage:
				g.Log().Cat(consts.DEBUG).Debug(ctx, "Received CloseMessage... ")
				return

			case websocket.BinaryMessage:
				err = stt.SendBuffer(ctx, msg)
				act = consts.ActionSend
				if err != nil {
					fnFailedClose(act, err)
					return
				}

			case websocket.TextMessage:
				g.Log().Cat(consts.INFO).Infof(ctx, "msgType:%v msg:%v", msgType, gconv.String(msg))
				var req *gjson.Json
				req, err = gjson.LoadContent(msg)

				if err != nil {
					g.Log().Cat(consts.ERROR).Error(ctx, "The message format is incorrect")
					err = gerror.NewCode(consts.ErrorFormatWrong)
					act = consts.ActionRead
					fnFailedClose(act, err)
					return
				}

				act = req.Get("action", "").String()

				switch gstr.ToLower(act) {
				default:
					err = gerror.NewCode(consts.ErrorRequestWrong)
					fnFailedClose(act, err)
					return
				case consts.ActionStart:
					var cmd *model.SttStartReq
					_ = req.Scan(&cmd)
					var startAck *model.SttActionAck
					// assign callid to the context
					ctx = context.WithValue(ctx, "callID", cmd.CallID)
					startAck, err = stt.Start(ctx, cmd, func(ctx context.Context, data any) {

						// write recognition result

						g.Log().Cat(consts.DEBUG).Debugf(ctx, "Recognition result:%v", gjson.New(data).MustToJsonIndentString())
						g.TryCatch(
							ctx,
							func(ctx context.Context) {

								err = ws.WriteJSON(data)

								if err != nil {
									fnFailedClose(act, err)
									stt.Interrupt(ctx)
									return
								}

							},
							func(ctx context.Context, exception error) {
								g.Log().Cat(consts.ERROR).Error(ctx, exception)
							},
						)

					}, func(ctx context.Context, data any) {
						//recognition finished
						g.Log().Cat(consts.DEBUG).Debugf(ctx, "Recognition finished:%v", gjson.New(data).MustToJsonIndentString())
						g.TryCatch(
							ctx,
							func(ctx context.Context) {
								err = ws.WriteJSON(data)
								if err != nil {
									fnFailedClose(act, err)
									return

								}
							},
							func(ctx context.Context, exception error) {
								g.Log().Cat(consts.ERROR).Error(ctx, exception)
							},
						)

					})

					if err != nil {
						fnFailedClose(act, err)
						StarAckTimeoutCount.Append(1)
						vCnt, e := g.Cfg().Get(ctx, "vendor.star_failed_cnt", 3)
						startFailedCnt := 3
						if e != nil {
							g.Log().Error(ctx, e)
						} else {
							startFailedCnt = vCnt.Int()
						}
						if StarAckTimeoutCount.Sum() >= startFailedCnt {
							pid := gproc.Pid()
							processes, processError := process.Processes()
							if processError == nil {
								for _, p := range processes {
									if p.Pid == gconv.Int32(pid) {
										cpuUsage, cpuErr := p.CPUPercentWithContext(ctx)
										if cpuErr == nil {
											g.Log().Debugf(ctx, "The cpu usage  :%v ", cpuUsage)

										}
										break
									}

								}
							}
							g.Log().Debug(ctx, "STMesh process is killed ... ")
							os.Exit(-1)
						}
						return
					}

					err = ws.WriteJSON(startAck)
					if err != nil {
						fnFailedClose(act, err)
						return
					}
				case consts.ActionStop:
					var cmd *model.SttStopReq
					_ = req.Scan(&cmd)
					var stopAck *model.SttActionAck
					stopAck, err = stt.Stop(ctx, cmd)
					if err != nil {
						fnFailedClose(act, err)
						return
					}
					g.TryCatch(
						ctx,
						func(ctx context.Context) {

							err = ws.WriteJSON(stopAck)

							if err != nil {
								fnFailedClose(act, err)
								return
							}
						},
						func(ctx context.Context, exception error) {
							g.Log().Cat(consts.ERROR).Error(ctx, exception)
						},
					)

				}

			}

		}
	}

}
