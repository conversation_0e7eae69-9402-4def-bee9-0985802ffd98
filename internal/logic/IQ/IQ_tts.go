package IQ

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"net/http"
)

type TTS struct {
	client     *gclient.Client
	profileKey string
}

func NewIQTTS(profileKey string) (*TTS, error) {
	o := &TTS{
		client: g.Client(),
	}
	if g.<PERSON>mpty(profileKey) {
		o.profileKey = `vendor.IQ.TTS`
	} else {
		o.profileKey = fmt.Sprintf(consts.CfgKeyIQTTS, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil

}

func (iq *TTS) Connect(ctx context.Context) (err error) {
	return
}

func (iq *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {
	g.Log().Cat(consts.INFO).Cat(consts.IQ).Infof(ctx, "IQ_TTS: %v", gjson.New(params).MustToJsonIndentString())
	vUrl, _ := g.Cfg().Get(ctx, iq.profileKey+".http.url", "")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(consts.ErrorGeneralError, "The tts url is  empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	var req *model.TtsReq
	var ret = &model.TtsRes{}

	_ = gconv.Scan(params, &req)
	vParams, _ := g.Cfg().Get(ctx, iq.profileKey+".http.params", "")
	if vParams.IsEmpty() {
		err = gerror.NewCode(consts.ErrorGeneralError, "The tts parameters is  empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		err = gerror.WrapCode(consts.ErrorGeneralError, err)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	language := func() string {
		if g.IsEmpty(req.Language) {
			return jsParams.Get("lang", "zh-tw").String()
		}
		return req.Language
	}()
	speed := func() string {
		if req.Speed == 0 {
			return jsParams.Get("speed", "100").String()
		}

		return gconv.String(req.Speed)
	}()
	volume := func() string {
		if req.Volume == 0 {
			return jsParams.Get("volume", "100").String()
		}
		return gconv.String(req.Volume)
	}()
	pitch := func() string {
		return jsParams.Get("pitch", "100").String()
	}()

	outputFmt := func() string {
		if !g.IsEmpty(req.Type) {
			return req.Type
		}
		return jsParams.Get("aformat", "wav").String()
	}()

	sampleRate := func() string {
		return jsParams.Get("srate", "16000").String()
	}()
	txtFormat := func() string {
		if g.IsEmpty(req.TextFormat) {
			return jsParams.Get("textformat", "text").String()
		} else {
			if gstr.ToLower(req.TextFormat) != "ssml" && gstr.ToLower(req.TextFormat) != "text" {
				return "text"
			}
		}
		return req.TextFormat

	}()
	speaker := func() string {
		return jsParams.Get("voice", "yafang").String()
	}()

	inputParams := g.MapStrStr{
		"voice":      speaker,
		"text":       gstr.Trim(req.Text),
		"lang":       language,
		"speed":      speed,
		"volume":     volume,
		"pitch":      pitch,
		"aformat":    outputFmt,
		"srate":      sampleRate,
		"textformat": txtFormat,
	}

	g.Log().Cat(consts.DEBUG).Cat(consts.IQ).Debugf(ctx, "IQ_TTS .original params : %s", gjson.New(inputParams).MustToJsonIndentString())

	r, err := iq.client.Get(
		ctx,
		vUrl.String(),
		inputParams,
	)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		err = gerror.WrapCode(consts.ErrorGeneralError, err)
		return
	}
	defer r.Close()

	if r.Response.StatusCode == http.StatusOK {
		ret.Buf = r.ReadAll()
		ret.FileName, ret.Ext, _ = utility.WriteToFile(ctx, req.Text, outputFmt, ret.Buf)
		res = ret

	} else {
		g.Log().Cat(consts.DEBUG).Cat(consts.EM).Debugf(ctx, " Response status : %d ", r.Response.StatusCode)
		err = gerror.NewCode(consts.ErrorGeneralError)
	}

	return

}
