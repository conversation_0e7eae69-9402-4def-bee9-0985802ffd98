// Package cyberon provides an implementation of a Speech-to-Text (STT) service using Cyberon technology.
package cyberon

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"

	"github.com/gogf/gf/v2/text/gstr"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	cyberon "SonaMesh/manifest/protobuf"
	. "SonaMesh/utility"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type TTS struct {
	conn       *grpc.ClientConn
	client     cyberon.StreamServiceClient
	profileKey string
}

func NewCyberonTTS(profileKey string) (*TTS, error) {
	o := &TTS{}
	if g.<PERSON>(profileKey) {
		o.profileKey = `vendor.Cyberon.TTS`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyCyberonTTS, profileKey)
	}

	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

/*
Function Name: Connect
Description: Establishes a connection to the TTS host.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - err: An error that indicates whether the connection was successful.
Exceptions:
   - The function logs information about connecting to the host.
   - It retrieves the URL from the configuration.
   - It creates a TLS configuration with insecure skip verification.
   - It establishes a gRPC connection to the specified URL using the TLS configuration.
   - If an error occurs during the connection, it logs the error and returns it.
*/

func (cy *TTS) Connect(ctx context.Context) (err error) {
	g.Log().Cat(INFO).Cat(CY).Info(ctx, "Connect to host")
	vUrl, _ := g.Cfg().Get(ctx, cy.profileKey+".grpc.url", "")
	if vUrl.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The url is not set ")
		err = gerror.NewCode(ErrorParamsError, "url is empty")
		return
	}

	// 從配置文件讀取 TLS 設置，預設為安全模式
	vInsecureSkipVerify, _ := g.Cfg().Get(ctx, cy.profileKey+".tls.insecure_skip_verify", false)
	vMinVersion, _ := g.Cfg().Get(ctx, cy.profileKey+".tls.min_version", "1.2")

	// 設置 TLS 最小版本
	minVersion := tls.VersionTLS12
	switch vMinVersion.String() {
	case "1.0":
		minVersion = tls.VersionTLS10
	case "1.1":
		minVersion = tls.VersionTLS11
	case "1.2":
		minVersion = tls.VersionTLS12
	case "1.3":
		minVersion = tls.VersionTLS13
	}

	config := &tls.Config{
		InsecureSkipVerify: vInsecureSkipVerify.Bool(),
		MinVersion:         uint16(minVersion),
	}
	cy.conn, err = grpc.Dial(vUrl.String(), grpc.WithTransportCredentials(credentials.NewTLS(config)))
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	cy.client = cyberon.NewStreamServiceClient(cy.conn)

	return
}

/*
Function Name: Synthesis
Description: Performs TTS synthesis based on the provided parameters.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Input parameters for TTS synthesis.
Output Parameters:
   - res: TTS synthesis result.
   - err: An error that indicates whether the synthesis was successful.
Exceptions:
   - The function logs information about the TTS synthesis.
   - It initializes a TtsReq object from the input parameters.
   - It checks if the text is already in the cache.
   - If the text is in the cache and the corresponding file exists, it returns the file content directly.
   - If the file does not exist, it removes the text from the cache.
   - It retrieves TTS parameters from the configuration.
   - It creates a TtsRequest object with the necessary parameters for TTS synthesis.
   - It establishes a TTS stream with the gRPC client and sends the TtsRequest.
   - It reads the stream response and collects the TTS data in a buffer.
   - If configured to save the voice file, it writes the data to a file.
   - It returns the TTS data and, if saved, the file name in the TtsRes object.
*/

func (cy *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {
	g.Log().Cat(INFO).Cat(CY).Infof(ctx, "Cyberon_TTS : %v", gjson.New(params).MustToJsonIndentString())
	defer cy.conn.Close()
	var req *model.TtsReq
	ret := &model.TtsRes{}
	_ = gconv.Scan(params, &req)
	if req == nil {
		err = gerror.NewCode(ErrorGeneralError, "Failed to convert parameters to tts request")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	vParams, _ := g.Cfg().Get(ctx, cy.profileKey+".http.params", "")
	if vParams.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The tts parameters is  empty")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	outputType := func() string {
		if g.IsEmpty(req.Type) {
			return jsParams.Get("outfmt", "wav").String()
		}
		return req.Type
	}()
	language := func() string {
		if g.IsEmpty(req.Language) {
			return jsParams.Get("language", "zh-TW").String()
		} else {
			if gstr.ToLower(req.Language) == "zh-tw" {
				return "zh-TW"
			}
		}

		return req.Language
	}()
	speed := func() float32 {
		if req.Speed == 0 {
			return jsParams.Get("speed", 1).Float32()
		}
		return req.Speed
	}()

	request := cyberon.TtsRequest{
		ServiceName: jsParams.Get("serviceName", "e2e").String(),
		Text:        req.Text,
		Outfmt:      outputType,
		VbrQuality:  jsParams.Get("vbr_quality", 4).Int64(),
		Language:    language,
		Phrbrk:      jsParams.Get("phrbrk", false).Bool(),
		Speaker:     jsParams.Get("speaker", "Sharon").String(),
		Speed:       speed,
		Gain:        jsParams.Get("gain", 1).Float32(),
		Token:       jsParams.Get("token", "").String(),
		Uid:         req.UserId,
	}

	g.Log().Cat(DEBUG).Cat(CY).Debugf(ctx, "Request: %v ", gjson.New(request).MustToJsonIndentString())

	stream, err := cy.client.TTS(ctx, &request)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	var buf bytes.Buffer
	for {
		resp, e := stream.Recv()
		if io.EOF == e {
			break
		}

		//if err != nil {
		//	err = gerror.WrapCode(ErrorGeneralError, err)
		//	g.Log().Cat(ERROR).Error(ctx, err)
		//	return nil, err
		//}

		buf.Write(resp.Data)

	}

	ret.Buf = buf.Bytes()
	ret.FileName, ret.Ext, _ = WriteToFile(ctx, req.Text, outputType, ret.Buf)
	res = ret
	return
}
