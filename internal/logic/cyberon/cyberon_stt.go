// Package cyberon provides an implementation of the STT (Speech-to-Text) service using Cyberon technology.
package cyberon

import "C"

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/text/gstr"
	"strings"
	"time"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/utility"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/gorilla/websocket"
)

type STT struct {
	conn             *websocket.Conn
	readerPool       *grpool.Pool
	ackFunc          AckFunc
	resultFunc       ResultFunc
	isGetPartial     bool
	isInterrupted    bool
	isShortCommand   bool
	chShortCommand   chan *model.ShortCommand
	isProactive      bool
	isAlreadyStopped bool
	profileKey       string
	errorCodes       *garray.Array
}

// NewCyberonSTT creates a new instance of the STT service with the provided AckFunc and ResultFunc handlers.
// The function initializes the STT struct with a reader pool, an acknowledgment handler, and a result handler.
//
// Parameters:
// - ackHandler: A function of type AckFunc that handles acknowledgment events.
// - resultHandler: A function of type ResultFunc that handles result events.
//
// Returns:
// - A pointer to a new STT instance with the provided handlers.
func NewCyberonSTT(ackHandler AckFunc, resultHandler ResultFunc, profileKey string) (*STT, error) {
	o := &STT{
		readerPool: grpool.New(),
		ackFunc:    ackHandler,
		resultFunc: resultHandler,
		errorCodes: garray.New(true),
	}
	if g.IsEmpty(profileKey) {
		o.profileKey = `vendor.Cyberon.STT`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyCyberonSTT, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)

	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

func (cy *STT) initializeErrorCodes(ctx context.Context) {
	vCodes, _ := g.Cfg().Get(ctx, cy.profileKey+".error_codes")
	if vCodes != nil && !vCodes.IsEmpty() {
		var codes []*model.ErrorCodeDef
		_ = vCodes.Structs(&codes)
		if codes != nil && len(codes) > 0 {
			for _, code := range codes {
				cy.errorCodes.Append(code)
			}
		}
	}
}
func (cy *STT) getErrorCodeMessage(code int) string {
	ret := ""
	cy.errorCodes.Iterator(func(k int, v interface{}) bool {
		var codeDef *model.ErrorCodeDef
		_ = gconv.Struct(v, &codeDef)
		if codeDef != nil && codeDef.Code == code {
			ret = codeDef.Message
			return false
		}
		return true
	})
	return ret
}

/*
Function Name: connectHost
Description: Establishes a connection to the STT service host using WebSocket, performs a handshake, and checks the initial acknowledgment.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - c: WebSocket connection to the STT service host.
   - err: An error indicating the success or failure of the connection setup.
Exceptions:
   - If the URL is not set, the function returns an error indicating that the URL is empty.
   - If dialing the WebSocket connection fails, the function returns an error.
   - If reading the initial acknowledgment fails, the function returns an error.
   - If the acknowledgment contains an error code, the function returns an error with the message "system error."
   - If the state in the acknowledgment is not "listening," the function returns an error with a message containing the actual state.
*/

func (cy *STT) connectHost(ctx context.Context) (c *websocket.Conn, err error) {
	g.Log().Cat(INFO).Cat(CY).Info(ctx, "Connect to host")
	vUrl, _ := g.Cfg().Get(ctx, cy.profileKey+".ws.url", "")
	if vUrl.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The url is not set ")
		err = gerror.NewCode(ErrorParamsError, "url is empty")
		return
	}
	client := gclient.NewWebSocket()
	client.Proxy = nil
	vHandShakeTimeout, _ := g.Cfg().Get(ctx, "websocket.hand_shake_timeout", "45s")
	client.HandshakeTimeout = vHandShakeTimeout.Duration()
	c, _, err = client.Dial(vUrl.String(), nil)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	var ack *gjson.Json
	err = c.ReadJSON(&ack)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, ack.MustToJsonIndentString())

	if ack.Contains("err_code") && ack.Get("err_code").Int() != 0 {
		err = gerror.NewCode(ErrorGeneralError, "system error")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	if ack.Get("state").String() == StateListening {
		// success and return
		return
	} else {
		err = gerror.NewCodef(ErrorGeneralError, "The state is %s", ack.Get("state").String())
		g.Log().Cat(ERROR).Error(ctx, err)
	}

	return
}

/*
Function Name: Connect
Description: Establishes a connection to the STT service host and starts a goroutine to handle incoming messages.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - err: An error indicating the success or failure of the connection setup.
Exceptions:
   - If the connection to the STT service host fails, the function returns an error.
   - If starting the goroutine for handling incoming messages fails, the function returns an error.
*/

func (cy *STT) Connect(ctx context.Context) (err error) {
	cy.conn, err = cy.connectHost(ctx)
	if err != nil {
		return
	}
	// initialize error codes
	cy.initializeErrorCodes(ctx)
	// start reading goroutine
	_ = cy.readerPool.AddWithRecover(ctx, cy.onMessage, func(ctx context.Context, exception error) {
		g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, exception)
	})

	return
}

func (cy *STT) waitShortCommandRecog(ctx context.Context) {
	g.Log().Cat(INFO).Cat(CY).Info(ctx, "Wait recognition of short command ...")
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".short_command_recog_timeout", "5s")
	defer func() {
		if cy.chShortCommand != nil {
			close(cy.chShortCommand)
		}
	}()
	fnFinish := func() {
		ack := &model.AckRes{}
		ack.Message.Type = "ack"
		ack.Status = ErrorState.Code()
		ack.Ack = ActionFinish
		if cy.ackFunc != nil {
			cy.ackFunc(ctx, ack)
		}
	}

	fnResult := func(r string) {
		result := &model.ResultRes{}
		result.Message.Type = "result"
		result.Result.Likelihood = 1.0
		result.Result.Transcript = r
		result.Final = true
		result.Status = ErrorOK.Code()
		if cy.resultFunc != nil {
			cy.resultFunc(ctx, result)
		}
	}

	var r *model.ShortCommand
	g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "Wait recognize result ... ")
	select {
	case r = <-cy.chShortCommand:
	case <-time.After(vTimeout.Duration()):
		g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "Identify short command timeout")
		fnFinish()
		if cy.conn != nil {
			_ = cy.conn.WriteJSON(gjson.New(CyberonStopCommand))
		}
		return
	}
	g.Log().Cat(DEBUG).Cat(CY).Debugf(ctx, "Recognized Results Of Short Instructions:%s", r.Text)
	fnResult(r.Text)
	if cy.conn != nil {
		_ = cy.conn.WriteJSON(gjson.New(CyberonStopCommand))
	}
}

// onMessage is a goroutine that listens for incoming messages from the WebSocket server.
// It processes JSON messages based on their "state" field and performs appropriate actions.
// The possible states are "listening", "result", and unknown states.
//
// - "listening": Indicates that the connection is successful or has stopped. Sends an acknowledgment to the frontend.
// - "result": Contains the recognition result. Sends the result to the frontend.
// - unknown state: Closes the connection, resets it, and sends an error acknowledgment to the frontend.
//
// This function should be called with a context.
func (cy *STT) onMessage(ctx context.Context) {
	// start listening for messages returned by ws server
	// 1. the message format is json
	// 2. When the connect is successful, you will receive {"state":"listening"} once
	// 3. When voice buffer data is sent and the result can be identified, json is returned where {"state":"result"}
	// 4 When canceled, {"state":"listening"} is received again
	// 5.After an identification is completed, state= result isFinished = true is received.
	g.Log().Cat(INFO).Cat(CY).Info(ctx, "Start receive message... ")
	defer func() {
		g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "Leave receive message... ")
	}()

	if cy.conn != nil {
		for {
			var data *gjson.Json

			if err := cy.conn.ReadJSON(&data); err != nil {
				g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, err)
				return
			}
			if cy.isInterrupted {

				_ = cy.conn.Close()
				cy.isInterrupted = false
				cy.isShortCommand = false
				cy.conn = nil
				cy.isProactive = false
				cy.isAlreadyStopped = false

				return

			}

			g.Log().Cat(DEBUG).Cat(CY).Debugf(ctx, "Receive response: %v", data.MustToJsonIndentString())
			if data != nil {
				if data.Contains("err_code") {
					if data.Get("err_code").Int() != 0 {
						// intermediate delivery issues identified
						err := gerror.NewCode(ErrorGeneralError, data.Get("err_msg").String())
						g.Log().Cat(ERROR).Cat(CY).Error(ctx, err)
						if cy.ackFunc != nil {
							ack := &model.AckRes{
								Message: struct {
									Type string `json:"type"`
								}(struct{ Type string }{Type: "ack"}),
								Ack:    ActionFinish,
								Status: ErrorGeneralError.Code(),
							}

							cy.ackFunc(ctx, ack)
						}
					}
				}
				if data.Contains("state") {
					switch data.Get("state").String() {
					default:
						// Unknown state, disconnect from host and return error ack to front end
						//_ = cy.conn.Close()
						//cy.conn = nil

						g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "Disconnect from host")
						if cy.ackFunc != nil {
							ack := &model.AckRes{
								Message: struct {
									Type string `json:"type"`
								}(struct{ Type string }{Type: "ack"}),
								Ack:    ActionFinish,
								Status: ErrorState.Code(),
							}

							cy.ackFunc(ctx, ack)
						}
						return

					case StateListening: // Stop, (the ack of connect is processed when the connection is already connected)
						if cy.ackFunc != nil && (cy.isProactive || cy.isInterrupted) {
							ack := &model.AckRes{
								Message: struct {
									Type string `json:"type"`
								}(struct{ Type string }{Type: "ack"}),
								Ack:    ActionStop,
								Status: ErrorOK.Code(),
							}
							if !cy.isInterrupted {
								// if it is forced to terminate it will not be sent
								cy.ackFunc(ctx, ack)
							}

							g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "Stop recognition ,disconnect from host")
							_ = cy.conn.Close()
							cy.isInterrupted = false
							cy.isShortCommand = false
							cy.conn = nil
							cy.isProactive = false
							cy.isAlreadyStopped = false
							return
						} else {
							g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "receive passive stop... ")
							cy.isAlreadyStopped = true

						}

					case StateResult: // return identification results
						result := &model.ResultRes{}
						if data.Get("err_code").Int() == 0 {
							if data.Get("isFinish").Bool() {
								result.Message.Type = "result"
								result.Result.Likelihood = 1.0
								result.Final = true
								result.Status = ErrorOK.Code()
								result.Result.Transcript = data.Get("recog_result").String()
								if cy.isShortCommand == false {
									if cy.resultFunc != nil && cy.isInterrupted == false {
										cy.resultFunc(ctx, result)
									}
								} else {
									// trigger short command recognition results
									cy.chShortCommand <- &model.ShortCommand{Text: result.Result.Transcript}
								}

							}
						} else {
							// failure
							result.Message.Type = "result"
							result.Result.Likelihood = 0.0
							result.Final = true
							result.Status = ErrorGeneralError.Code()
							result.Result.Transcript = cy.getErrorCodeMessage(data.Get("err_code").Int())
							if cy.resultFunc != nil && cy.isInterrupted == false {
								cy.resultFunc(ctx, result)
							}
						}
					}
				}
			}

		}
	}
}

/*
Function Name: Start
Description: Initiates the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Parameters required to start the STT process, including the call ID and enablePartial flag.
Output Parameters:
   - res: Response from the STT process.
   - err: Error, if any, occurred during the initiation of the STT process.
Exceptions:
   - The function logs information about the initiation process and errors encountered.
   - The function requires a connection to the host for the STT process to start correctly.
   - The parameters required for the STT process must be set in the configuration.
   - The function sends an acknowledgment (ack) message if an acknowledgment function is provided.
*/

func (cy *STT) Start(ctx context.Context, params any) (err error) {
	g.Log().Cat(INFO).Cat(CY).Infof(ctx, "Cybreron_STT_Start: %+v ", params)
	fnReturnStartAck := func(statusCode int) {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStart,
			Status: statusCode,
		}
		if cy.ackFunc != nil {
			cy.ackFunc(ctx, ack)
		}
	}
	if cy.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
		return
	}

	var req *model.SttStartReq
	_ = gconv.Scan(params, &req)
	vParams, _ := g.Cfg().Get(ctx, cy.profileKey+".ws.start_params", "")
	if vParams.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The parameters is not set")
		err = gerror.NewCode(ErrorParamsError, "params is empty")
		fnReturnStartAck(ErrorParamsError.Code())
		return
	}
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil || jsParams == nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorParamsError, err, "Parameter setting format is not json format")
		fnReturnStartAck(ErrorParamsError.Code())
		return
	}

	_ = jsParams.Set("action", ActionStart)
	_ = jsParams.Set("uid", req.CallID)
	_ = jsParams.Set("isGetPartial", req.EnablePartial)
	if !g.IsEmpty(gstr.Trim(req.Params)) {
		kv := utility.SplitParamsToMap(req.Params)
		if len(kv) > 0 {
			// 固定 key 為'domainID'
			if v, ok := kv["domainID"]; ok {
				_ = jsParams.Set("domain", v)
			}
		}
	}

	cy.isGetPartial = req.EnablePartial

	g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, jsParams.MustToJsonIndentString())
	err = cy.conn.WriteJSON(jsParams)
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
		return
	}
	if req.ShortCommand {
		cy.isShortCommand = true
		cy.chShortCommand = make(chan *model.ShortCommand, 1)
		_ = cy.readerPool.AddWithRecover(
			ctx,
			cy.waitShortCommandRecog,
			func(ctx context.Context, exception error) {
				g.Log().Cat(ERROR).Error(ctx, exception)
			},
		)
	}
	cy.isProactive, cy.isAlreadyStopped = false, false
	fnReturnStartAck(ErrorOK.Code())

	return
}

/*
Function Name: Stop
Description: Stops the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Parameters required to stop the STT process, including the call ID.
Output Parameters:
   - res: Response from the STT process.
   - err: Error, if any, occurred during the stopping of the STT process.
Exceptions:
   - The function logs information about the stopping process and errors encountered.
   - The function requires a connection to the host for the STT process to stop correctly.
   - The function sends an acknowledgment (ack) message when the STT process is stopped.
*/

func (cy *STT) Stop(ctx context.Context, params any) (err error) {
	g.Log().Cat(INFO).Cat(CY).Infof(ctx, "Cyberon_STT_STOP: %+v", params)
	if cy.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	var req *model.SttStopReq
	_ = gconv.Scan(params, &req)

	if err = cy.conn.WriteJSON(req); err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	if cy.isAlreadyStopped {
		cy.isAlreadyStopped = false
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStop,
			Status: ErrorOK.Code(),
		}
		if cy.ackFunc != nil {
			cy.ackFunc(ctx, ack)
		}
		return

	}

	cy.isProactive = true

	// return ack in onmessage

	return
}

/*
Function Name: SendVoiceBuffer
Description: Sends the voice buffer to the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - buf: Voice buffer containing audio data to be processed by the STT.
Output Parameters:
   - err: Error, if any, occurred during the sending of the voice buffer to the STT process.
Exceptions:
   - The function logs information about the length of the voice buffer and errors encountered.
   - The function requires a connection to the host for the STT process to receive the voice buffer.
*/

func (cy *STT) SendVoiceBuffer(ctx context.Context, buf []byte) (err error) {
	// g.Log().Cat(INFO).Cat(CY).Infof(ctx, "Cyberon_STT_VocBuf: length-> %d ", len(buf))
	if cy.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	err = cy.conn.WriteMessage(websocket.BinaryMessage, buf)
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
	}

	return
}

// RecognizeFile is a method of the STT struct that processes a WAV file for speech-to-text recognition.
// It takes two input parameters:
//   - ctx: A context.Context for logging and error handling purposes.
//   - file: A string representing the path to the WAV file to be processed.
// The function returns two output parameters:
//   - res: An interface{} containing the recognition result.
//   - err: An error value indicating if any error occurred during the recognition process.
//
// The function first checks if the input file is in 16-bit little-endian PCM format. If not, it returns an error.
// Then, it establishes a connection to the host and sends the necessary JSON parameters to start the recognition process.
// It reads the content of the WAV file into a byte buffer and sends it to the host for processing.
// The function listens for text messages from the host, parses the JSON response, and logs the results.
// If the recognition process is complete, it returns the recognition result.
// In case of any errors during the process, the function logs the error and returns the error value.

func (cy *STT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	g.Log().Cat(INFO).Cat(CY).Infof(ctx, "Recognize file: %s", gjson.New(params).MustToJsonString())
	if !utility.Is16BitWav(ctx, params.FileName) {
		err = gerror.NewCode(ErrorGeneralError, "The file format is not 16 bit")
		return
	}

	conn, err := cy.connectHost(ctx)
	if err != nil {
		return
	}

	vParams, _ := g.Cfg().Get(ctx, cy.profileKey+".ws_recognize_file.start_params")
	if vParams.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The parameters is not set")
		err = gerror.NewCode(ErrorParamsError, "params is empty")
		return
	}

	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil || jsParams == nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorParamsError, err, "Parameter setting format is not json format")
		return
	}
	_ = jsParams.Set("action", ActionStart)
	_ = jsParams.Set("uid", gctx.CtxId(ctx))
	g.Log().Cat(DEBUG).Cat(CY).Debugf(ctx, "Request:%v", jsParams.MustToJsonIndentString())

	err = conn.WriteJSON(jsParams)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	buf := gfile.GetBytes(params.FileName)
	if buf == nil {
		err = gerror.NewCode(ErrorGeneralError, "Failed to read file")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	var sessionID = ""
	if !params.WaitResult {
		sessionID = grand.S(32)
		res = sessionID

	}
	var result = make(chan string, 1)

	ctxNeverDone := gctx.NeverDone(ctx)
	isWaitResult := params.WaitResult
	_ = cy.readerPool.AddWithRecover(
		ctxNeverDone,
		func(ctx context.Context) {
			text := ""
			sbContents := strings.Builder{}

			err := gerror.NewCode(ErrorOK)
			defer func() {
				if conn != nil {
					_ = conn.Close()
				}
			}()
			fnSendFailed := func() {

				if !isWaitResult && !g.IsEmpty(params.WebHook) {
					g.Client().ContentJson().PostContent(ctx, params.WebHook, g.Map{
						"code":       gerror.Code(err).Code(),
						"message":    gerror.Code(err).Message(),
						"text":       "",
						"session_id": sessionID,
					})
				} else {

					result <- sbContents.String()
				}
			}

			for {
				err = conn.WriteMessage(websocket.BinaryMessage, buf)
				if err != nil {
					g.Log().Cat(ERROR).Error(ctx, err)
					err = gerror.WrapCode(ErrorGeneralError, err)
					fnSendFailed()
					return
				}

				err = conn.WriteJSON(gjson.New(`{"action":"stop"}`))

				if err != nil {
					g.Log().Cat(ERROR).Error(ctx, err)
					err = gerror.WrapCode(ErrorGeneralError, err)
					fnSendFailed()
					return
				}

				msgType, msg, err := conn.ReadMessage()
				if err != nil {
					g.Log().Cat(ERROR).Error(ctx, err)
					err = gerror.WrapCode(ErrorGeneralError, err)
					fnSendFailed()
					return
				}
				if msgType == websocket.TextMessage {
					jsResult, err := gjson.LoadContent(msg)
					if err != nil {
						g.Log().Cat(ERROR).Error(ctx, err)
						err = gerror.WrapCode(ErrorGeneralError, err)
						return
					}
					g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, jsResult.MustToJsonIndentString())
					if jsResult.Get("state").String() == "listening" {
						g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "End of document recognition...")
						result <- sbContents.String()
						return
					}
					if jsResult.Get("err_code").Int() != 0 {
						err = gerror.NewCode(ErrorGeneralError, jsResult.Get("err_msg").String())
						fnSendFailed()
						return
					}
					if jsResult.Get("state").String() == StateResult {

						text = jsResult.Get("recog_result").String()
						if !isWaitResult {
							if !g.IsEmpty(params.WebHook) && jsResult.Get("isFinish").Bool() {
								g.Client().ContentJson().PostContent(ctx, params.WebHook, g.Map{
									"code":       ErrorOK.Code(),
									"message":    ErrorOK.Message(),
									"text":       text,
									"session_id": sessionID,
								})
							}
						} else {

							sbContents.WriteString(text)
						}

					}

				}

			}
		},
		func(ctx context.Context, exception error) {
			g.Log().Cat(ERROR).Cat(CY).Error(ctx, exception)
			result <- exception.Error()
		},
	)
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".action_ack_timeout", "1m")

	if params.WaitResult {
		select {
		case res = <-result:
			return
		case <-time.After(vTimeout.Duration()):
			err = gerror.NewCode(ErrorGeneralError, "Timeout ...")
			return "", err

		}
	}

	return
}

func (cy *STT) Interrupt(ctx context.Context) {
	g.Log().Cat(INFO).Cat(CY).Info(ctx, "Cyberon_STT: interrupting...")
	cy.isInterrupted = true
	if cy.conn == nil {
		g.Log().Cat(DEBUG).Cat(CY).Debug(ctx, "The connection to the cyberon server has been disconnected")
		return
	}
	_ = cy.conn.WriteJSON(gjson.New(CyberonCancelCommand))
}
