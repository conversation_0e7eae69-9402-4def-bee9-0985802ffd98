package tts

import (
	"SonaMesh/internal/logic/IQ"
	"SonaMesh/internal/logic/iii"
	"context"
	"fmt"

	"SonaMesh/internal/consts"
	"SonaMesh/internal/logic/azure"
	"SonaMesh/internal/logic/cyberon"
	"SonaMesh/internal/logic/emotibot"
	"SonaMesh/internal/logic/interfaces"
	"SonaMesh/internal/model"
	"SonaMesh/internal/service"

	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

type sTts struct{}

func init() {
	service.RegisterTts(New())

	ctx := gctx.GetInitCtx()
	// asynchronously load into cache
	g.Go(
		ctx,
		loadIntoCache,
		func(ctx context.Context, exception error) {
			g.Log().Cat(consts.ERROR).Error(ctx, exception)
		},
	)

	// Start the timer, regularly check whether the file time has expired, and update the global cache at the same time
	vDur, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".record_duration", "30m")
	bSave, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".save_voice_file", true)
	if bSave.Bool() {
		gtimer.SetInterval(ctx, vDur.Duration(), updateRecordFromCache)
	}
}

/*
Function Name: updateRecordFromCache
Description: Updates records from cache and saves them to a file.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - None
Exceptions:
   - The function logs information about updating records from cache.
   - It checks and removes expired voice files from cache.
   - It retrieves the save folder path from the configuration.
   - If the save folder path is not empty, it creates a file path for records in that folder.
   - If the record file does not exist, the function logs a debug message and returns.
   - It retrieves data from the cache and creates a JSON string.
   - The function saves the JSON string to the record file.
   - If an error occurs during file writing, the function logs an error.
*/

func updateRecordFromCache(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "Update record from cache...")
	sFolder, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".save_folder", "")

	checkVoiceFilesExpired(ctx)

	if !sFolder.IsEmpty() {
		recFile := gfile.Join(sFolder.String(), consts.RecordFile)
		mData := gcache.MustData(ctx)
		records := gjson.New(mData)
		err := gfile.PutContents(recFile, records.String())
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
		}

	}
}

/*
Function Name: checkVoiceFilesExpired
Description: Checks and removes expired voice files from cache and file system.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - None
Exceptions:
   - The function logs information about checking expired voice files.
   - It retrieves the save folder path and keep duration from the configuration.
   - It creates a map to store cache key-value pairs (key: file path, value: text).
   - It retrieves all cache keys and iterates over them.
   - For each cache key, it gets the corresponding value and adds an entry to the map.
   - It scans the files in the save folder and compares their last modified time with the keep duration.
   - If a file is expired, it removes the corresponding entry from the cache and deletes the file.
   - After processing all files, the function iterates over the map and adds entries back to the cache.
*/

func checkVoiceFilesExpired(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "Check voice files  expired...")
	sFolder, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".save_folder", "")
	vDur, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".keep_duration", "1d")
	mCache := gmap.NewStrAnyMap()
	keys := gcache.MustKeyStrings(ctx)

	// gcache key is text, value is the full path of the file
	// Key in mCache is the full path of the file and value is text.
	// After physically deleting the timeout file, delete it from mCache and put it back into the global cache.

	if !sFolder.IsEmpty() {
		files, err := gfile.ScanDirFile(sFolder.String(), "*", true)
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			return
		}

		for _, key := range keys {
			v, e := gcache.Get(ctx, key)
			if e == nil {
				mCache.Set(v.String(), key)
			}

		}

		for _, file := range files {
			mTime := gtime.New(gfile.MTime(file))

			if gtime.Now().Sub(mTime) >= vDur.Duration() {
				mCache.Remove(file)
				if err = gfile.Remove(file); err != nil {
					g.Log().Cat(consts.ERROR).Error(ctx, err)
					continue
				}

			}
		}
	}

	mCache.Iterator(func(k string, v interface{}) bool {
		_ = gcache.Set(ctx, v, k, gcache.DurationNoExpire)
		return true
	})
}

/*
Function Name: loadIntoCache
Description: Loads voice file information from a file into the cache.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - None
Exceptions:
   - The function logs information about loading into the cache.
   - It retrieves the save folder path from the configuration.
   - It constructs the file path for the record file.
   - If the record file exists, it reads its contents.
   - If the contents are not empty, it loads the JSON content into a map.
   - For each entry in the map (text, voice file), it adds the entry to the cache.
*/

func loadIntoCache(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "Load into cache...")
	sFolder, _ := g.Cfg().Get(ctx, consts.CfgKeyTTS+".save_folder", "")

	if !sFolder.IsEmpty() {
		recFile := gfile.Join(sFolder.String(), consts.RecordFile)
		if gfile.Exists(recFile) {
			contents := gfile.GetContents(recFile)
			if !g.IsEmpty(contents) {
				// load from file record
				jsRecords, err := gjson.LoadContent([]byte(contents))
				if err != nil {
					g.Log().Cat(consts.DEBUG).Debug(ctx, err)
					return
				}

				m := jsRecords.MapStrAny()

				for text, vocFile := range m {
					_ = gcache.Set(ctx, text, vocFile, gcache.DurationNoExpire)
				}

			}

		}
	}
}

func New() *sTts {
	return &sTts{}
}

/*
Function Name: findFromCache
Description: Finds a file from the cache based on the provided content.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - content: The text content for which to find the corresponding cached file.
Output Parameters:
   - fileName: The name of the cached file found.
   - err: An error that indicates whether the file was found or if an error occurred during the process.
Exceptions:
   - The function logs a debug message indicating the start of the file search.
   - It checks if the content is present in the cache using the `gcache.Contains` function.
   - If the content is found in the cache, it retrieves the full file name from the cache.
   - It checks if the file exists using `gfile.Exists`.
   - If the file exists, it sets the `fileName` variable and returns.
   - If the file does not exist, it removes the content from the cache and logs an error.
   - If the content is not found in the cache, it logs a debug message indicating that the text is not in the cache.
*/

func (s *sTts) findFromCache(ctx context.Context, content string) (fileName string, err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Find file from cache, Text:%s", content)

	// find from cache
	isExist, _ := gcache.Contains(ctx, content)

	if isExist {
		fullName, _ := gcache.Get(ctx, content)

		if gfile.Exists(fullName.String()) {
			fileName = fullName.String()
			return
		} else {
			// remove from cache if file does not exist
			_, _ = gcache.Remove(ctx, content)
			err = gerror.NewCodef(consts.ErrorGeneralError, "The file %s is not exist", fullName.String())
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			return
		}
	}
	err = gerror.NewCode(consts.ErrorGeneralError, "The text not in cache...")
	g.Log().Cat(consts.DEBUG).Debug(ctx, err)

	return
}

// tts initializes a text-to-speech (TTS) provider based on the specified vendor and returns the TTS interface.
//
// Context: The context to carry deadlines, cancellations, and other request-scoped values.
// vccID (string): The VCC ID associated with the TTS configuration.
// routeAccessCode (string): The route access code, if applicable.
//
// Returns:
//   tts (interfaces.ITTSFace): The initialized TTS provider that implements the ITTSFace interface.
//   err (error): An error, if any, encountered during the initialization process.

func (s *sTts) tts(ctx context.Context, vccID, routeAccessCode string) (tts interfaces.ITTSFace, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Get tts object with vccID: %v routeAccessCode: %v", vccID, routeAccessCode)
	if g.IsEmpty(vccID) {
		err = gerror.NewCode(consts.ErrorGeneralError, "VccID is empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	cfgTtsKey := ""
	suffix := ""
	if !g.IsEmpty(routeAccessCode) && !g.IsEmpty(vccID) {
		cfgTtsKey = fmt.Sprintf("tenant_%s.rac_%s.TTS", vccID, routeAccessCode)
		suffix = fmt.Sprintf("%v_%v", vccID, routeAccessCode)
	} else if g.IsEmpty(routeAccessCode) && !g.IsEmpty(vccID) {
		cfgTtsKey = fmt.Sprintf("default_%s.TTS", vccID)
		suffix = fmt.Sprintf("%v", vccID)
	} else {
		cfgTtsKey = "default.TTS"
	}

	vVendor, _ := g.Cfg().Get(ctx, cfgTtsKey, "")

	if vVendor.IsEmpty() {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "There is no setting corresponding to the tenant'tts :%s ,then use default settings", cfgTtsKey)
		cfgTtsKey = "default.TTS"
		suffix = ""
		vVendor, _ = g.Cfg().Get(ctx, cfgTtsKey, "")
		if vVendor.IsEmpty() {
			err = gerror.NewCodef(consts.ErrorGeneralError, "There is no default setting :%s", cfgTtsKey)
			g.Log().Cat(consts.ERROR).Error(ctx, err)

			return
		}

	}

	switch gstr.ToLower(vVendor.String()) {
	default:
		err = gerror.NewCodef(consts.ErrorGeneralError, "Unsupported vendors: %s ", vVendor.String())
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	case gstr.ToLower(consts.VendorAzure):
		tts, err = azure.NewAzureTTS(suffix)
	case gstr.ToLower(consts.VendorEmotiBot):
		tts, err = emotibot.NewEmotiBotTTS(suffix)
	case gstr.ToLower(consts.VendorCyberon):
		tts, err = cyberon.NewCyberonTTS(suffix)
	case gstr.ToLower(consts.VendorIQ):
		tts, err = IQ.NewIQTTS(suffix)
	case gstr.ToLower(consts.VendorIII):
		tts, err = iii.NewIIITTS(suffix)
	}

	return
}

/*
Function Name: Synthesis
Description: Performs TTS synthesis by interacting with the TTS service.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: TTS request parameters, including VccId and RouteAccessCode.
Output Parameters:
   - res: TTS response containing the synthesized audio data.
   - err: An error that indicates whether the TTS synthesis operation was successful.
Exceptions:
   - The function logs information about the TTS synthesis request.
   - It calls the `tts` function to obtain a TTS instance with the specified VccId and RouteAccessCode.
   - It establishes a connection to the TTS service using the obtained TTS instance.
   - It calls the `Synthesis` method of the TTS instance to perform TTS synthesis.
   - It scans the result into the `res` variable.
*/

func (s *sTts) Synthesis(ctx context.Context, params *model.TtsReq) (res *model.TtsRes, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Synthesis: %v", gjson.New(params).MustToJsonIndentString())
	// First check whether it exists in the cache, and if it exists, return it directly
	vocFile, e := s.findFromCache(ctx, params.Text)

	if e == nil {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Synthesis find text from cache , file is :%v", vocFile)
		res = &model.TtsRes{
			Buf:      gfile.GetBytes(vocFile),
			FileName: gfile.Basename(vocFile),
			Ext:      gfile.ExtName(vocFile),
		}
		return
	}

	tts, err := s.tts(ctx, params.VccId, params.RouteAccessCode)
	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
		return nil, err
	}
	if err = tts.Connect(ctx); err != nil {
		return
	}
	params.Text = s.replaceSign(ctx, params.Text)
	r, err := tts.Synthesis(ctx, params)

	if err != nil {
		return nil, err
	}
	_ = gconv.Scan(r, &res)

	return
}

func (s *sTts) replaceSign(ctx context.Context, text string) string {
	retText := text
	vLeftSign, err := g.Cfg().Get(ctx, "TTS.left_side_sign", `^L`)
	if err != nil {
		return retText
	}
	vRightSign, err := g.Cfg().Get(ctx, "TTS.right_side_sign", `^R`)
	if err != nil {
		return retText
	}
	retText = gstr.ReplaceByMap(text, map[string]string{
		vLeftSign.String():  `<`,
		vRightSign.String(): `>`,
	})

	return retText

}
