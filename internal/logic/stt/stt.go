package stt

import (
	"SonaMesh/internal/logic/iii"
	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"

	"SonaMesh/internal/consts"
	"SonaMesh/internal/logic/azure"
	"SonaMesh/internal/logic/cyberon"
	"SonaMesh/internal/logic/emotibot"
	"SonaMesh/internal/logic/interfaces"
	"SonaMesh/internal/model"
	"SonaMesh/internal/service"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

type sSTT struct {
	startActionAckChan chan *model.SttActionAck
	stopActionAckChan  chan *model.SttActionAck
	sttObj             interfaces.ISTTFace
	onResultFunc       consts.ResultFunc
	onFinishFunc       consts.AckFunc
	positiveStopFlag   bool
	buffer             bytes.Buffer
	callID             string
	shortCommand       bool
}

func init() {
	service.RegisterSTT(New())
}

func New() *sSTT {
	v := &sSTT{}
	return v
}

/*
Function Name: stt_ws
Description: Gets the STT (Speech-to-Text) object based on the specified VccID and routeAccessCode.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - vccID: The VccID associated with the Speech-to-Text (STT) service.
   - routeAccessCode: The route access code associated with the STT service (optional).
Output Parameters:
   - stt_ws: The STT interface representing the STT service object.
   - err: An error that indicates whether the STT service object retrieval was successful or if an error occurred.
Exceptions:
   - The function logs an informational message indicating the start of the STT service object retrieval process.
   - It checks if the VccID is empty and returns an error if it is.
   - It constructs the configuration key (`cfgSttKey`) based on the VccID and routeAccessCode.
   - It retrieves the STT vendor from the configuration using `g.Cfg().Get`.
   - It checks if the STT vendor is empty and returns an error if it is.
   - Based on the STT vendor, the function creates an instance of the corresponding STT implementation (e.g., EmotiBotSTT, CyberonSTT).
   - The STT object is returned along with any error that occurred during the process.
*/

func (s *sSTT) stt(ctx context.Context, vccID, routeAccessCode string) (stt interfaces.ISTTFace, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Get stt  object with vc·cID: %v routeAccessCode: %v", vccID, routeAccessCode)
	if g.IsEmpty(vccID) {
		err = gerror.NewCode(consts.ErrorGeneralError, "VccID is empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	cfgSttKey := ""
	suffix := ""

	if !g.IsEmpty(routeAccessCode) && !g.IsEmpty(vccID) {
		cfgSttKey = fmt.Sprintf("tenant_%s.rac_%s.STT", vccID, routeAccessCode)
		suffix = fmt.Sprintf("%v_%v", vccID, routeAccessCode)
	} else if g.IsEmpty(routeAccessCode) && !g.IsEmpty(vccID) {
		cfgSttKey = fmt.Sprintf("default_%s.STT", vccID)
		suffix = fmt.Sprintf("%v", vccID)
	} else {
		cfgSttKey = "default.STT"
	}

	vVendor, _ := g.Cfg().Get(ctx, cfgSttKey, "")

	if vVendor.IsEmpty() {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "There is no setting corresponding to the tenant'stt :%s ,then use default settings", cfgSttKey)
		cfgSttKey = "default.STT"
		suffix = ""
		vVendor, _ = g.Cfg().Get(ctx, cfgSttKey, "")
		if vVendor.IsEmpty() {
			err = gerror.NewCodef(consts.ErrorGeneralError, "There is no  default setting :%s", cfgSttKey)
			g.Log().Cat(consts.ERROR).Error(ctx, err)

			return
		}

	}

	suffix = gstr.Trim(suffix)
	switch gstr.ToLower(vVendor.String()) {
	default:
		err = gerror.NewCodef(consts.ErrorGeneralError, "Unsupported vendors: %s ", vVendor.String())
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	case gstr.ToLower(consts.VendorAzure):
		stt, err = azure.NewAzureSTT(s.ackHandler, s.resultHandler, suffix)
	case gstr.ToLower(consts.VendorEmotiBot):
		stt, err = emotibot.NewEmSTT(s.ackHandler, s.resultHandler, suffix)
	case gstr.ToLower(consts.VendorCyberon):
		stt, err = cyberon.NewCyberonSTT(s.ackHandler, s.resultHandler, suffix)
	case gstr.ToLower(consts.VendorIII):
		stt, err = iii.NewIIISTT(s.ackHandler, s.resultHandler, suffix)
	}
	return
}

func (s *sSTT) ackHandler(ctx context.Context, message any) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Action Ack: %v", gjson.New(message).MustToJsonIndentString())
	var ack *model.SttActionAck
	_ = gconv.Scan(message, &ack)
	if ack != nil {
		//fnIsChannelClosed := func(ch <-chan *model.SttActionAck) bool {
		//	select {
		//	case <-ch:
		//		return false
		//	default:
		//		return true
		//	}
		//}

		switch ack.Ack {
		default:
			g.Log().Cat(consts.DEBUG).Debugf(ctx, "Unknown ack : %v", ack.Ack)
		case consts.ActionStart:
			// If start ack has been triggered, just trigger finish directly.
			s.startActionAckChan <- ack

			return

		case consts.ActionStop:
			// If stop ack has been triggered, just trigger finish directly.
			s.stopActionAckChan <- ack
			return
		case consts.ActionFinish:
			//  trigger finish ack
			//  If the stop channel is in the open state, start stop ack
			if s.onFinishFunc != nil {
				g.Log().Cat(consts.DEBUG).Debug(ctx, "Trigger on finished function ... ")
				s.onFinishFunc(ctx, ack)
			}
			if s.sttObj != nil {
				g.Log().Cat(consts.DEBUG).Debug(ctx, "Positive to stop...")
				_ = s.sttObj.Stop(ctx, &model.SttStopReq{Action: consts.ActionStop})
				s.positiveStopFlag = true
			}

		}
	}
}

func (s *sSTT) resultHandler(ctx context.Context, result any) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Recognize result: %v", gjson.New(result).MustToJsonIndentString())
	vRedirect, _ := g.Cfg().Get(ctx, "vendor.STT.recognize_result_to_redis", false)
	var regResult *model.RegResult
	var sipxResult any
	var redisResults []*model.Record

	if gjson.New(result).Contains("to_sipx") {
		_ = gconv.Struct(result, &regResult)
		sipxResult = regResult.ToSIPX

		_ = gconv.Structs(regResult.ToRedis, &redisResults)

	} else {
		sipxResult = result

	}

	if s.shortCommand == false && vRedirect.Bool() {

		g.Log().Cat(consts.INFO).Infof(ctx, "Redirect result to redis")
		contextForRedis := gctx.NeverDone(ctx)
		if exist, err := g.Redis().Exists(contextForRedis, s.callID); err != nil {
			g.Log().Cat(consts.ERROR).Error(contextForRedis, err)
		} else if exist == 1 {
			// update append value to the list
			if v, e := g.Redis().Get(contextForRedis, s.callID); e != nil {
				g.Log().Cat(consts.ERROR).Error(contextForRedis, e)
			} else {
				var catchResult *model.RedisResult
				_ = v.Struct(&catchResult)
				if catchResult != nil && redisResults != nil && len(redisResults) > 0 {
					catchResult.DataRecord = append(catchResult.DataRecord, redisResults...)
					vExpired, _ := g.Cfg().Get(ctx, "vendor.STT.expire_duration", "1d")
					if e := g.Redis().SetEX(contextForRedis, s.callID, catchResult, int64(vExpired.Duration().Seconds())); e != nil {
						g.Log().Cat(consts.ERROR).Error(contextForRedis, e)
					}

				}

			}

		} else {
			// insert new value
			if _, e := g.Redis().Set(contextForRedis, s.callID, &model.RedisResult{
				CallID:     s.callID,
				DataRecord: redisResults,
			}); e != nil {
				g.Log().Cat(consts.ERROR).Error(contextForRedis, e)
			}

		}

		// publish
		if _, e := g.Redis().Publish(contextForRedis, s.callID, gjson.New(redisResults)); e != nil {
			g.Log().Cat(consts.ERROR).Error(contextForRedis, e)
		}

		return
	}

	if s.onResultFunc != nil && sipxResult != nil {
		s.onResultFunc(ctx, sipxResult)
	}
}

/*
Function Name: Start
Description: Initiates the Speech-to-Text (STT) process by sending a start request and waiting for the corresponding action acknowledgment.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: The SttStartReq structure containing the parameters for starting the STT process.
Output Parameters:
   - ack: The SttActionAck structure representing the acknowledgment received for the start action.
   - err: An error that indicates whether the STT start action was successful or if an error occurred.
Exceptions:
   - The function logs an informational message with the details of the start action parameters.
   - It retrieves the action acknowledgment timeout duration from the configuration using `g.Cfg().Get`.
   - The function calls the `stt_ws` method to get the STT service object based on the provided VccID and routeAccessCode.
   - If an error occurs during the STT service object retrieval, it returns the error.
   - It establishes a connection to the STT service using `s.sttObj.Connect`.
   - If an error occurs during the connection process, it returns the error.
   - It starts the STT process using `s.sttObj.Start`.
   - The function uses a select statement with a timeout to wait for the action acknowledgment (`ack`) or a timeout error (`err`).
   - If the acknowledgment is received within the specified timeout, it logs a debug message with the details of the acknowledgment.
   - If a timeout occurs, it returns an error indicating that the start action acknowledgment timed out.
*/

func (s *sSTT) Start(ctx context.Context, params *model.SttStartReq, resultHandler consts.ResultFunc, finishHandler consts.AckFunc) (ack *model.SttActionAck, err error) {
	//mu := sync.Mutex{}
	//mu.Lock()
	//defer mu.Unlock()

	g.Log().Cat(consts.INFO).Infof(ctx, "Speech to text - start: %v", gjson.New(params).MustToJsonIndentString())
	vTimeout, _ := g.Cfg().Get(ctx, consts.CfgKeySTT+".action_ack_timeout", "1m")

	if s.sttObj != nil {
		ack = &model.SttActionAck{}
		ack.Ack = "start"
		ack.Status = consts.ErrorOK.Code()
		ack.Message.Type = "ack"

		return

	}
	if params != nil {
		s.callID = params.CallID
		s.shortCommand = params.ShortCommand
	}

	s.sttObj, err = s.stt(ctx, params.VccID, params.RouteAccessCode)
	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
		return nil, err
	}
	ctx = context.WithValue(ctx, "role", params.Role)
	if err = s.sttObj.Connect(ctx); err != nil {
		return
	}

	s.onResultFunc = resultHandler
	s.onFinishFunc = finishHandler

	s.startActionAckChan = make(chan *model.SttActionAck, 1)

	if err = s.sttObj.Start(ctx, params); err != nil {
		return nil, err
	}
	s.positiveStopFlag = false

	select {
	case ack = <-s.startActionAckChan:
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Start Ack: %v", gjson.New(ack).MustToJsonIndentString())
		return
	case <-time.Tick(vTimeout.Duration()):
		err = gerror.NewCode(consts.ErrorGeneralError, "Start action ack timeout")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
	}

	return
}

/*
Function Name: SendBuffer
Description: Sends a voice buffer to the Speech-to-Text (STT) service for processing.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - buf: The byte slice representing the voice buffer to be sent to the STT service.
Output Parameters:
   - err: An error that indicates whether sending the voice buffer was successful or if an error occurred.
Exceptions:
   - The function logs an informational message indicating the initiation of sending a voice buffer.
   - If the STT service object (`s.sttObj`) is not initialized, it returns an error indicating that the start action should be called first.
   - It sends the voice buffer to the STT service using `s.sttObj.SendVoiceBuffer`.
   - If an error occurs during the process of sending the voice buffer, it returns the error.
*/

func (s *sSTT) SendBuffer(ctx context.Context, buf []byte) (err error) {
	//g.Log().Cat(consts.INFO).Infof(ctx, "Send voice buffer ,buffer length %v  ...", len(buf))
	if s.sttObj == nil {
		err = gerror.NewCodef(consts.ErrorGeneralError, "Please call start first ")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	bSave, _ := g.Cfg().Get(ctx, "vendor.STT.save_voice_buffer", false)

	if bSave.Bool() {
		sPath, _ := g.Cfg().Get(ctx, "vendor.STT.save_folder", "./buf")
		keyName := gctx.CtxId(ctx)
		callID := gconv.String(ctx.Value("callID"))
		if !g.IsEmpty(callID) {
			keyName = callID
		}

		fullName := gfile.Join(sPath.String(), fmt.Sprintf("%s.wav", keyName))
		_ = gfile.PutBytesAppend(fullName, buf)
	}

	err = s.sttObj.SendVoiceBuffer(ctx, buf)
	if err != nil {
		return
	}

	return
}

/*
Function Name: Stop
Description: Stops the Speech-to-Text (STT) process by sending a stop request and waiting for the corresponding action acknowledgment.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: The SttStopReq structure containing the parameters for stopping the STT process.
Output Parameters:
   - ack: The SttActionAck structure representing the acknowledgment received for the stop action.
   - err: An error that indicates whether the STT stop action was successful or if an error occurred.
Exceptions:
   - The function logs an informational message with the details of the stop action parameters.
   - It retrieves the action acknowledgment timeout duration from the configuration using `g.Cfg().Get`.
   - If the STT service object (`s.sttObj`) is not initialized, it returns an error indicating that the start action should be called first.
   - It sends a stop request to the STT service using `s.sttObj.Stop`.
   - If an error occurs during the stop process, it returns the error.
   - The function uses a select statement with a timeout to wait for the action acknowledgment (`ack`) or a timeout error (`err`).
   - If the acknowledgment is received within the specified timeout, it logs a debug message with the details of the acknowledgment.
   - After receiving the acknowledgment, it sets the STT service object to `nil`.
*/

func (s *sSTT) Stop(ctx context.Context, params *model.SttStopReq) (ack *model.SttActionAck, err error) {
	//mu := sync.Mutex{}
	//mu.Lock()
	//defer mu.Unlock()

	g.Log().Cat(consts.INFO).Infof(ctx, "Speech to text - stop: %v", gjson.New(params).MustToJsonIndentString())
	vTimeout, _ := g.Cfg().Get(ctx, consts.CfgKeySTT+".action_ack_timeout", "1m")

	if s.positiveStopFlag {
		g.Log().Cat(consts.INFO).Info(ctx, "Positive stopped ,then return stop ack")
		ack = &model.SttActionAck{}
		ack.Ack = consts.ActionStop
		ack.Status = 0
		ack.Message.Type = consts.StateAck
		s.positiveStopFlag = false
		return

	}

	if s.sttObj == nil {
		err = gerror.NewCodef(consts.ErrorGeneralError, "Please call start first ")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	defer func() { s.sttObj = nil }()
	err = s.sttObj.Stop(ctx, params)
	if err != nil {
		return
	}
	// wait for ack result
	s.stopActionAckChan = make(chan *model.SttActionAck, 1)

	select {
	case ack = <-s.stopActionAckChan:
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Stop action ack: %v", gjson.New(ack).MustToJsonIndentString())
		s.sttObj = nil
		return

	case <-time.Tick(vTimeout.Duration()):
		err = gerror.NewCode(consts.ErrorGeneralError, "Stop action ack timeout")
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
	}

	return
}

func (s *sSTT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Recognize file :%v", gjson.New(params).MustToJsonString())
	stt, err := s.stt(ctx, params.Vccid, params.RouteAccessCode)
	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
		return nil, err
	}
	return stt.RecognizeFile(ctx, params)
}

func (s *sSTT) Interrupt(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "Interrupting ... ")
	if s.sttObj != nil {
		s.sttObj.Interrupt(ctx)
	}
	defer func() { s.sttObj = nil }()
}
