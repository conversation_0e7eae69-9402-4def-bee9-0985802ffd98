package interfaces

import (
	"SonaMesh/internal/model"
	"context"
)

type ISTTFace interface {
	Connect(ctx context.Context) (err error)
	Start(ctx context.Context, params any) (err error)
	Stop(ctx context.Context, params any) (err error)
	SendVoiceBuffer(ctx context.Context, buf []byte) (err error)
	RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error)
	Interrupt(ctx context.Context)
}

type ITTSFace interface {
	Connect(ctx context.Context) (err error)
	Synthesis(ctx context.Context, params any) (res any, err error)
}
