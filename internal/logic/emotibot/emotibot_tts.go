package emotibot

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"net/http"
)

type TTS struct {
	client     *gclient.Client
	profileKey string
}

func NewEmotiBotTTS(profileKey string) (*TTS, error) {
	o := &TTS{client: g.Client()}
	if g.Is<PERSON>mpty(profileKey) {
		o.profileKey = `vendor.EmotiBot.TTS`
	} else {
		o.profileKey = fmt.Sprintf(consts.CfgKeyEmotiBotTTS, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

func (em *TTS) Connect(ctx context.Context) (err error) {

	return
}

/*
Function Name: Synthesis
Description: Synthesizes text to speech using the EmotiBot TTS service.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Parameters for the text-to-speech synthesis.
Output Parameters:
   - res: The result of the text-to-speech synthesis, including the audio data and file name.
   - err: An error that indicates whether the synthesis was successful or if an error occurred during the process.
Exceptions:
   - The function logs an informational message indicating the start of the EmotiBot TTS process.
   - It retrieves the TTS URL from the configuration using `g.Cfg().Get`.
   - It checks if the TTS URL is empty and returns an error if it is.
   - It scans the parameters using `gconv.Scan` to extract the TTS request parameters.
   - It retrieves the TTS parameters from the configuration using `g.Cfg().Get`.
   - It checks if the TTS parameters are empty and returns an error if they are.
   - It creates a JSON object (`jsParams`) from the loaded TTS parameters using `gjson.LoadContent`.
   - It defines functions to extract optional parameters from the request or use default values.
   - It sends a GET request to the TTS URL with the extracted parameters using `em.client.Get`.
   - It checks if the response status code is OK (200).
   - If the response is successful, it reads the audio data and writes it to a file using `utility.WriteToFile`.
   - It returns the result containing the audio data and file name.
   - If the response status code is not OK, it logs a debug message and returns an error.
*/

func (em *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {

	g.Log().Cat(consts.INFO).Cat(consts.EM).Infof(ctx, "EmotiBot_TTS: %v", gjson.New(params).MustToJsonIndentString())
	vUrl, _ := g.Cfg().Get(ctx, em.profileKey+".http.url", "")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(consts.ErrorGeneralError, "The tts url is  empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	var req *model.TtsReq
	var ret = &model.TtsRes{}

	_ = gconv.Scan(params, &req)
	vParams, _ := g.Cfg().Get(ctx, em.profileKey+".http.params", "")
	if vParams.IsEmpty() {
		err = gerror.NewCode(consts.ErrorGeneralError, "The tts parameters is  empty")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		err = gerror.WrapCode(consts.ErrorGeneralError, err)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	language := func() string {
		if g.IsEmpty(req.Language) {
			return jsParams.Get("language", "zh-tw").String()
		}
		return req.Language
	}()
	speed := func() float32 {
		if req.Speed == 0 {
			return jsParams.Get("speed", "6").Float32()
		}
		return req.Speed
	}()
	volume := func() float32 {
		if req.Volume == 0 {
			return jsParams.Get("volume", 3).Float32()
		}
		return req.Volume
	}()
	pitch := func() float32 {
		return jsParams.Get("pitch", 5).Float32()
	}()
	outputFmt := func() string {
		if !g.IsEmpty(req.Type) {
			return req.Type
		}
		return jsParams.Get("type", "wav").String()
	}()
	sampleRate := func() int {
		return jsParams.Get("samplerate", 16000).Int()
	}()

	params = g.Map{
		"text":       gstr.Trim(req.Text),
		"user_id":    req.UserId,
		"language":   language,
		"speed":      speed,
		"volume":     volume,
		"pitch":      pitch,
		"type":       outputFmt,
		"samplerate": sampleRate,
	}
	g.Log().Cat(consts.DEBUG).Cat(consts.EM).Debugf(ctx, "EmotiBot_TTS .original params : %s", gjson.New(params).MustToJsonIndentString())
	r, err := em.client.Get(
		ctx,
		vUrl.String(),
		params,
	)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		err = gerror.WrapCode(consts.ErrorGeneralError, err)
		return
	}
	defer r.Close()

	if r.Response.StatusCode == http.StatusOK {
		ret.Buf = r.ReadAll()
		ret.FileName, ret.Ext, _ = utility.WriteToFile(ctx, req.Text, outputFmt, ret.Buf)
		res = ret

	} else {
		g.Log().Cat(consts.DEBUG).Cat(consts.EM).Debugf(ctx, " Response status : %d ", r.Response.StatusCode)
		err = gerror.NewCode(consts.ErrorGeneralError)
	}

	return
}
