package emotibot

import (
	"context"
	"fmt"
	"time"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/gorilla/websocket"
)

type STT struct {
	ackFunc        AckFunc
	resultFunc     ResultFunc
	readerPoll     *grpool.Pool
	conn           *websocket.Conn
	isInterrupt    bool
	isShortCommand bool
	chShortCommand chan *model.ShortCommand
	isProactive    bool
	profileKey     string
}

func NewEmSTT(ackHandler AckFunc, resultHandler ResultFunc, profileKey string) (*STT, error) {
	o := &STT{
		readerPoll: grpool.New(),
		ackFunc:    ackHandler,
		resultFunc: resultHandler,
	}
	if g.IsEmpty(profileKey) {
		o.profileKey = `vendor.EmotiBot.STT`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyEmotiBotSTT, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

/*
Function Name: Connect
Description: Establishes a connection to the speech-to-text (STT) host.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - err: An error that may occur during the connection process.
Exceptions:
   - The function logs information about connecting to the host.
   - The function retrieves the STT host URL and handshake timeout duration from the configuration.
   - The function uses a WebSocket client to dial the STT host.
   - If the connection is successful, the function starts a goroutine to handle incoming messages.
   - If an error occurs during the connection process, the function logs the error, closes the connection, and sets the connection to nil.
*/

func (em *STT) Connect(ctx context.Context) (err error) {
	g.Log().Cat(INFO).Cat(EM).Info(ctx, "Connect to host")
	vUrl, _ := g.Cfg().Get(ctx, em.profileKey+".ws.url", "")
	if vUrl.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The url is not set ")
		err = gerror.NewCode(ErrorParamsError, "url is empty")
		return
	}
	vHandShakeTimeout, _ := g.Cfg().Get(ctx, "websocket.hand_shake_timeout", "45s")
	c := gclient.NewWebSocket()
	c.Proxy = nil
	c.HandshakeTimeout = vHandShakeTimeout.Duration()
	em.conn, _, err = c.Dial(vUrl.String(), nil)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	em.conn.SetCloseHandler(func(code int, text string) error {
		g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "closed  code:%d text:%s", code, text)
		return nil
	})

	defer func() {
		if err != nil {
			_ = em.conn.Close()
			em.conn = nil
		}
	}()

	// If the connection is successful, start the goroutine
	if err = em.readerPoll.AddWithRecover(ctx, em.onMessage, func(ctx context.Context, exception error) {
		g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, exception)
	}); err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	return
}

/*
Function Name: onMessage
Description: Handles incoming messages from the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
Output Parameters:
   - None
Exceptions:
   - The function logs information about starting and leaving the message reception process.
   - The function reads JSON messages from the STT connection and processes them based on their types.
   - The function supports messages of type `StateResult` and `StateAck`.
   - If the message type is `StateResult`, it checks for a successful and final recognition result and triggers the result function.
   - If the message type is `StateAck`, it converts an initialization acknowledgment to a start acknowledgment and triggers the acknowledgment function.
   - The function logs debug information about received messages, including details about the recognition result.
*/

func (em *STT) onMessage(ctx context.Context) {
	g.Log().Cat(INFO).Cat(EM).Info(ctx, "Start receive message... ")
	defer func() {
		g.Log().Cat(INFO).Cat(EM).Info(ctx, "Leave receive message... ")
		if em.conn != nil {
			_ = em.conn.Close()
			em.isProactive, em.isInterrupt, em.isShortCommand = false, false, false
		}
	}()

	if em.conn != nil {
		for {
			var data *gjson.Json
			if err := em.conn.ReadJSON(&data); err != nil {
				g.Log().Cat(ERROR).Error(ctx, err)
				return
			}
			if em.isInterrupt {
				g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Interrupted.... ")
				return
			}
			g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Receive response: %v", data.MustToJsonIndentString())
			if data != nil {
				messageType := data.Get("message.type").String()
				switch messageType {
				default:
					g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Unknown message type: %v", messageType)
				case StateFinish, StateError:
					// task ended error occurred disconnected
					g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Recevive %v ... ", messageType)

					statusCode := ErrorOK.Code()

					if messageType == StateError {
						statusCode = data.Get("status").Int()
					}

					action := ActionFinish
					if em.isProactive {
						action = ActionStop
					}

					if em.ackFunc != nil {

						ack := &model.AckRes{
							Message: struct {
								Type string `json:"type"`
							}(struct{ Type string }{Type: "ack"}),
							Ack:    action,
							Status: statusCode,
						}

						em.ackFunc(ctx, ack)
					}
					return
				case StateResult:
					// return recognition results
					// We will not make judgments about enable partial first, but it will depend on the test results.

					if data.Get("service").String() == "recognize" &&
						data.Get("status").Int() == 0 &&
						data.Get("result.final").Bool() {

						// successful and completed
						result := &model.ResultRes{}
						result.Message.Type = "result"
						result.Status = data.Get("status").Int()
						result.Final = data.Get("result.final").Bool()
						result.Result.Likelihood = data.Get("result.hypotheses.0.likelihood").Float32()
						result.Result.Transcript = data.Get("result.hypotheses.0.transcript").String()

						if em.resultFunc != nil && em.isInterrupt == false && em.isShortCommand == false {
							em.resultFunc(ctx, result)
						}
						if em.isShortCommand {
							// trigger short command recognition
							em.chShortCommand <- &model.ShortCommand{Text: result.Result.Transcript}
						}

					} else if data.Get("service").String() == "recognize" &&
						data.Get("status").Int() != 0 {
						// return failure
						result := &model.ResultRes{}
						result.Message.Type = "result"
						result.Status = data.Get("status").Int()
						if em.resultFunc != nil {
							em.resultFunc(ctx, result)
						}
					}

				case StateAck:

					// convert init ack to start ack and trigger
					fnAck := func(ackString string) {
						var ack *model.AckRes
						_ = gconv.Scan(data, &ack)
						if ack != nil {
							ack.Ack = ackString
							if em.ackFunc != nil {
								em.ackFunc(ctx, ack)
							}

						} else {
							g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Failed to convert ack res structure")
						}
					}

					ackType := data.Get("ack").String()
					switch ackType {
					case AckINIT:
						fnAck(ActionStart)
						if em.isShortCommand {
							// shortCommandRecognition
							em.chShortCommand = make(chan *model.ShortCommand, 1)
							_ = em.readerPoll.AddWithRecover(
								ctx,
								em.waitShortCommandRecog,
								func(ctx context.Context, exception error) {
									g.Log().Cat(ERROR).Error(ctx, exception)
								},
							)
						}

					case AckStop:

						if em.isProactive {
							fnAck(ActionStop)
							_ = em.conn.Close()
							em.conn = nil
							em.isProactive, em.isInterrupt, em.isShortCommand = false, false, false
						}

						if em.isInterrupt {
							em.isInterrupt = false
							_ = em.conn.Close()
							em.conn = nil
							em.isProactive, em.isInterrupt, em.isShortCommand = false, false, false
							return
						}

					}

				}
			}
		}
	}
}

/*
Function Name: Start
Description: Initiates the speech-to-text (STT) process with the provided parameters.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Parameters for starting the STT process.
Output Parameters:
   - res: The result of the STT process.
   - err: An error that may occur during the STT process.
Exceptions:
   - The function logs information about starting the STT process.
   - The function extracts parameters from the input and retrieves STT start parameters from the configuration.
   - The function sends an INIT request to the STT host with the provided parameters.
   - If an error occurs during the process, the function logs the error and returns an acknowledgment with the corresponding status code.
   - If the process is successful, the function returns an acknowledgment with a success status code.
*/

func (em *STT) Start(ctx context.Context, params any) (err error) {
	g.Log().Cat(INFO).Cat(EM).Infof(ctx, "EmotiBot_STT_Start: %#v ", params)

	fnReturnStartAck := func(statusCode int) {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    "start",
			Status: statusCode,
		}
		if em.ackFunc != nil {
			em.ackFunc(ctx, ack)
		}
	}

	if em.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
		return
	}
	var req *model.SttStartReq
	_ = gconv.Scan(params, &req)
	vParams, _ := g.Cfg().Get(ctx, em.profileKey+".ws.start_params", "")
	if vParams.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "The parameters is not set")
		err = gerror.NewCode(ErrorParamsError, "Params is empty")
		fnReturnStartAck(ErrorParamsError.Code())
		return
	}
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorParamsError, err, "Parameter setting format is not json format")
		fnReturnStartAck(ErrorParamsError.Code())
		return
	}

	if jsParams != nil {
		// send INIT request
		_ = jsParams.Set("service_params.recognize.enable_partial", req.EnablePartial)
		_ = jsParams.Set("session.call-id", req.CallID)
		_ = jsParams.Set("session.user-id", req.VccID)
	}
	err = em.conn.WriteJSON(jsParams)
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
		return
	}
	em.isShortCommand = req.ShortCommand

	// start ack is triggered in on message
	return
}

/*
Function Name: Stop
Description: Stops the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - params: Parameters for stopping the STT process (not used in this implementation).
Output Parameters:
   - res: The result of stopping the STT process (not used in this implementation).
   - err: An error that may occur during the STT process.
Exceptions:
   - The function logs information about stopping the STT process.
   - The function checks if there is an active connection to the host; if not, it returns an error indicating the lack of connection.
   - The function sends a STOP command to the STT host.
   - The acknowledgment for stopping the STT process is handled in the onMessage function.
*/

func (em *STT) Stop(ctx context.Context, params any) (err error) {
	g.Log().Cat(INFO).Cat(EM).Infof(ctx, "EmotiBot_STT_Stop: %v", params)
	if em.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	jsStop := gjson.New(EmotiStopCommand)

	if err = em.conn.WriteJSON(jsStop); err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}

	em.isProactive = true
	// onmessage returns ack

	return
}

/*
Function Name: SendVoiceBuffer
Description: Sends a voice buffer to the speech-to-text (STT) process.
Input Parameters:
   - ctx: Context to carry deadlines, cancellations, and other request-scoped values.
   - buf: Voice buffer to be sent for speech recognition.
Output Parameters:
   - err: An error that may occur during the process.
Exceptions:
   - The function logs information about sending the voice buffer for STT.
   - The function checks if there is an active connection to the host; if not, it returns an error indicating the lack of connection.
   - The function sends the voice buffer as a binary message to the STT host.
*/

func (em *STT) SendVoiceBuffer(ctx context.Context, buf []byte) (err error) {
	// g.Log().Cat(INFO).Infof(ctx, "EmotiBot_STT_VocBuf: length-> %d ", len(buf))

	if em.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	err = em.conn.WriteMessage(websocket.BinaryMessage, buf)
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		g.Log().Cat(ERROR).Error(ctx, err)
	}

	return
}

// RecognizeFile is a method of the STT struct that processes a WAV file for speech-to-text recognition using the EmotiBot API.
// It takes two input parameters:
//   - ctx: A context.Context for logging and error handling purposes.
//   - file: A string representing the path to the WAV file to be processed.
// The function returns two output parameters:
//   - res: An interface{} containing the recognition result.
//   - err: An error value indicating if any error occurred during the recognition process.
//
// The function first retrieves the API URL and parameters from the configuration.
// If either the URL or parameters are empty, it returns an error.
// It then reads the content of the WAV file into a byte buffer and sends a POST request to the API with the buffer as the payload.
// The function reads the API response, parses the JSON result, and logs the results.
// If the recognition process is complete, it returns the recognition result.
// If the API response indicates an error, it returns a recognition error.
// In case of any errors during the process, the function logs the error and wraps it with an appropriate error code.

func (em *STT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	//
	//return em.recognizeFileWS(ctx, file)
	return em.recognizeFileHttp(ctx, params)
}

func (em *STT) recognizeFileWS(ctx context.Context, file string) (res any, err error) {
	g.Log().Cat(INFO).Cat(EM).Infof(ctx, "Recognize file : %s", file)
	vUrl, _ := g.Cfg().Get(ctx, em.profileKey+".ws_recognize_file.url")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The url is empty")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	vParams, _ := g.Cfg().Get(ctx, em.profileKey+".ws_recognize_file.params")
	if vParams.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The params is empty")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	buf := gfile.GetBytes(file)
	if buf == nil {
		err = gerror.NewCode(ErrorGeneralError, "Failed to read file")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	s := gclient.NewWebSocket()
	s.Proxy = nil
	conn, _, err := s.Dial(vUrl.String()+vParams.String(), nil)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}

	defer conn.Close()

	vFormatParams, _ := g.Cfg().Get(ctx, em.profileKey+".ws_recognize_file.voc_format")
	jsFormat := gjson.New(vFormatParams.String())
	sampleRate := jsFormat.Get("sample_rate", 16000).Int()
	bitPerSample := jsFormat.Get("bit_per_sample", 16).Int()
	nChannels := jsFormat.Get("channels", 1).Int()
	sampleDur := jsFormat.Get("duration", 100).Int()
	bufSize := (sampleRate * bitPerSample * nChannels * sampleDur) / 1000
	g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Buffer size: %d , sample rate: %d bit:%d,channels:%d ,duration:%dms",
		bufSize,
		sampleRate,
		bitPerSample,
		nChannels,
		sampleDur,
	)
	result := make(chan *model.RecogFileResult, 1)

	g.Go(ctx, func(ctx context.Context) {
		for {

			var ret *gjson.Json
			e := conn.ReadJSON(&ret)
			if e != nil {
				g.Log().Cat(ERROR).Error(ctx, e)
				result <- &model.RecogFileResult{Error: e}
				return
			}
			g.Log().Cat(DEBUG).Debug(ctx, ret.MustToJsonIndentString())
			if ret.Get("message.type").String() == "result" &&
				ret.Get("status").Int() == 0 &&
				ret.Get("result.final").Bool() == true {
				result <- &model.RecogFileResult{
					Text: ret.Get("result.hypotheses.0.transcript").String(),
				}
			} else if ret.Get("status").Int() != 0 {
				result <- &model.RecogFileResult{Error: gerror.Newf("Error code :%d", ret.Get("status").Int())}
			}

		}
	}, func(ctx context.Context, exception error) {
		g.Log().Cat(ERROR).Error(ctx, exception)
	})
	// send SOS
	err = conn.WriteMessage(websocket.TextMessage, []byte(EmotiStartRecFileCommand))
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}

	loopTimes, startIndex := len(buf)/bufSize, 0
	for i := 0; i < loopTimes; i++ {
		err = conn.WriteMessage(websocket.BinaryMessage, buf[startIndex:startIndex+bufSize])
		if err != nil {
			err = gerror.WrapCode(ErrorGeneralError, err)
			g.Log().Cat(ERROR).Error(ctx, err)
			return
		}
		startIndex += bufSize

	}

	alreadyRead := loopTimes * bufSize
	if alreadyRead <= len(buf) {
		err = conn.WriteMessage(websocket.BinaryMessage, buf[alreadyRead:])
		if err != nil {
			err = gerror.WrapCode(ErrorGeneralError, err)
			g.Log().Cat(ERROR).Error(ctx, err)
			return
		}
	}
	g.Log().Cat(DEBUG).Debug(ctx, "Send file over ....")
	_ = conn.WriteMessage(websocket.TextMessage, []byte(EmotiStopRecFileCommand))

	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".action_ack_timeout", "1m")
	select {
	//case r := <-result:
	//if r.Error != nil {
	//	 err = gerror.WrapCode(ErrorGeneralError ,r.Error )
	//
	//}else{
	//	res = r.Text
	//}
	case <-time.After(vTimeout.Duration()):
		g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Timeout ...")
		err = gerror.NewCode(ErrorGeneralError, "timeout...")
	}
	//_ = conn.WriteMessage(websocket.TextMessage, []byte(EmotiStopRecFileCommand))

	return
}

func (em *STT) recognizeFileHttp(ctx context.Context, params *model.STTParams) (res any, err error) {
	g.Log().Cat(INFO).Cat(EM).Infof(ctx, "Recognize file: %s", gjson.New(params).MustToJsonString())
	vUrl, _ := g.Cfg().Get(ctx, em.profileKey+".http.url", "")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The url is empty")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	vParams, _ := g.Cfg().Get(ctx, em.profileKey+".http.params", "")
	if vParams.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The params is empty")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	UrlWithParams := fmt.Sprintf("%s?", vUrl.String())
	userID := grand.S(16)
	callID := grand.S(16)
	requestID := grand.S(16)

	for k, v := range gjson.New(vParams).Map() {
		UrlWithParams += fmt.Sprintf("%v=%v&", k, v)
	}
	UrlWithParams += fmt.Sprintf("user-id=%v&call-id=%v&request-id=%v", userID, callID, requestID)
	g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Recognize file , params:%s", UrlWithParams)
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".action_ack_timeout", "1m")
	// Use ffprobe to get the duration of the audio file as timeout
	cmdStr := fmt.Sprintf(CmdPattern, params.FileName)

	timeout := vTimeout.Duration()

	vocDur, e := gproc.ShellExec(ctx, cmdStr)
	if e != nil {
		g.Log().Cat(ERROR).Error(ctx, e)
	} else {
		vocDur = gstr.Replace(vocDur, "\n", "s")
		timeout, e = time.ParseDuration(vocDur)
		if e != nil {
			timeout = vTimeout.Duration()
		}
	}

	buf := gfile.GetBytes(params.FileName)

	var chResult = make(chan string, 1)

	if buf != nil {
		res = fmt.Sprintf("%s_%s_%s", callID, requestID, userID)
		ctxNeverDone := gctx.NeverDone(ctx)
		isWaitResult := params.WaitResult
		_ = em.readerPoll.AddWithRecover(
			ctxNeverDone,
			func(ctx context.Context) {
				// start goroutine asynchronous execution
				var resp *gclient.Response
				var err error
				resp, err = g.Client().Timeout(timeout).Post(ctx, UrlWithParams, buf)
				if err != nil {
					g.Log().Cat(ERROR).Error(ctx, err)
					err = gerror.WrapCode(ErrorGeneralError, err)
					if isWaitResult {
						chResult <- ""
					}
					return
				}

				defer resp.Close()

				ret := resp.ReadAllString()
				var jsResult *gjson.Json
				text := ""
				SID := ""

				defer func() {
					if !isWaitResult && !g.IsEmpty(params.WebHook) {

						d := g.Map{
							"code":       gerror.Code(err).Code(),
							"message":    gerror.Code(err).Message(),
							"text":       text,
							"session_id": SID,
						}
						g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Send to %s with data  %v", params.WebHook, d)
						g.Client().ContentJson().PostContent(ctx, params.WebHook, d)

					} else {
						chResult <- text
					}
				}()

				jsResult, err = gjson.LoadContent([]byte(ret))
				if err != nil {
					g.Log().Cat(ERROR).Error(ctx, err)
					err = gerror.WrapCode(ErrorGeneralError, err)
					return
				}

				g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, jsResult.MustToJsonIndentString())

				if jsResult.Get("message.type").String() == StateResult &&
					jsResult.Get("result.final").Bool() {
					text = jsResult.Get("result.hypotheses.0.transcript").String()
					SID = jsResult.Get("session_id").String()
				} else if jsResult.Get("message.type").String() == StateError {
					err = gerror.NewCode(ErrorGeneralError, "Recognition error")
				}

				if err == nil {
					err = gerror.NewCode(ErrorOK)
				}
			},
			func(ctx context.Context, exception error) {
				g.Log().Cat(ERROR).Cat(EM).Error(ctx, exception)
				if params.WaitResult {
					chResult <- exception.Error()
				}
			},
		)

	} else {
		err = gerror.NewCode(ErrorGeneralError, "Failed to read file...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	if params.WaitResult {
		select {
		case res = <-chResult:
			return
		case <-time.After(timeout):
			g.Log().Cat(ERROR).Cat(EM).Error(ctx, "timeout")
			err = gerror.NewCode(ErrorGeneralError, "timeout")
		}

	}

	return
}

func (em *STT) Interrupt(ctx context.Context) {
	g.Log().Cat(INFO).Cat(EM).Info(ctx, "EmotiBot_STT_Interrupt:...")
	em.isInterrupt = true
	if em.conn != nil {
		jsStop := gjson.New(EmotiStopCommand)
		_ = em.conn.WriteJSON(jsStop)

	}
}

func (em *STT) waitShortCommandRecog(ctx context.Context) {
	g.Log().Cat(INFO).Cat(EM).Info(ctx, "Wait recognition of short command ...")
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".short_command_recog_timeout", "5s")
	fnFinish := func() {
		ack := &model.AckRes{}
		ack.Message.Type = "ack"
		ack.Status = ErrorState.Code()
		ack.Ack = ActionFinish
		if em.ackFunc != nil {
			em.ackFunc(ctx, ack)
		}
	}

	fnResult := func(r string) {
		result := &model.ResultRes{}
		result.Message.Type = "result"
		result.Result.Likelihood = 1.0
		result.Result.Transcript = r
		result.Final = true
		result.Status = ErrorOK.Code()
		if em.resultFunc != nil {
			em.resultFunc(ctx, result)
		}
	}
	var r *model.ShortCommand
	g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Wait recognize result ... ")
	select {
	case r = <-em.chShortCommand:
	case <-time.After(vTimeout.Duration()):
		g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Identify short command timeout")
		fnFinish()
		if em.conn != nil {
			_ = em.conn.WriteJSON(gjson.New(EmotiStopCommand))
		}

		return

	}

	g.Log().Cat(DEBUG).Cat(EM).Debugf(ctx, "Recognized Results Of Short Instructions:%s", r.Text)
	fnResult(r.Text)
	if em.conn != nil {
		_ = em.conn.WriteJSON(gjson.New(EmotiStopCommand))
	}
}
