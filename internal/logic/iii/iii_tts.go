package iii

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/utility"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
	"net/http"
)

type TTS struct {
	client     *gclient.Client
	profileKey string
}

func NewIIITTS(profileKey string) (*TTS, error) {
	o := &TTS{
		client: gclient.New(),
	}
	if g.IsEmpty(profileKey) {
		o.profileKey = `vendor.iii.TTS`
	} else {
		o.profileKey = fmt.Sprintf(consts.CfgKeyIIIBotTTS, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)

	if err != nil {
		return nil, err
	}
	if v.<PERSON>() {
		return nil, gerror.Newf("please check the configuration , key %q ", o.profileKey)
	}
	return o, nil

}

func (i *TTS) logger(cat string) glog.ILogger {
	return g.Log().Cat(cat).Cat(consts.III)
}
func (i *TTS) Connect(ctx context.Context) (err error) {

	return
}

func (i *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {
	i.logger(consts.INFO).Infof(ctx, "iii_TTS: %v", params)
	vUrl, _ := g.Cfg().Get(ctx, i.profileKey+".url", "")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(consts.ErrorGeneralError, "The tts url is  empty")
		i.logger(consts.ERROR).Error(ctx, err)
	}

	var req *model.TtsReq
	var ret = &model.TtsRes{}
	_ = gconv.Struct(params, &req)
	if req == nil {
		return nil, gerror.NewCode(consts.ErrorGeneralError, "The tts parameters is  empty")
	}

	resp, err := i.client.PostForm(ctx, vUrl.String(), g.MapStrStr{"text": req.Text})
	if err != nil {
		i.logger(consts.ERROR).Error(ctx, err)
		return nil, gerror.WrapCode(consts.ErrorGeneralError, err)
	}
	defer resp.Close()

	if resp.StatusCode == http.StatusOK {
		ret.Buf = resp.ReadAll()
		ret.FileName, ret.Ext, _ = utility.WriteToFile(ctx, req.Text, "wav", ret.Buf)
		res = ret

	} else {
		i.logger(consts.DEBUG).Debug(ctx, err)
		err = gerror.NewCode(consts.ErrorGeneralError)
	}
	return

}
