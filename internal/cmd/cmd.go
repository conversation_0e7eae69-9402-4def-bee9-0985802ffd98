package cmd

import (
	"SonaMesh/internal/consts"
	"SonaMesh/internal/controller/stt"
	"SonaMesh/internal/controller/stt_ws"
	"SonaMesh/internal/controller/tts"
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var Main = gcmd.Command{
	Name:  "sonamesh",
	Usage: "sonamesh",
	Brief: "start sonamesh server",
	Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
		s := g.Server()

		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Middleware(recordsOfInteraction, ghttp.MiddlewareHandlerResponse)
			group.Bind(
				tts.NewV1(),
				stt.NewV1(),
			)
			group.ALL("/ws/stt", stt_ws.NewV1().WSProc)
		})

		s.Run()

		return nil
	},
}

func recordsOfInteraction(r *ghttp.Request) {
	ctx := r.GetCtx()
	if gjson.Valid(r.GetBodyString()) {
		g.Log().Cat(consts.IR).Debugf(ctx, `Request-Uri [%s] \n Body: %s`, r.RequestURI, r.GetBodyString())
	} else {
		g.Log().Cat(consts.IR).Debugf(ctx, "Request-Uri [%s]", r.RequestURI)
	}

	r.Middleware.Next()
	if gjson.Valid(r.Response.BufferString()) {
		g.Log().Cat(consts.IR).Debugf(ctx, `Response: \n %s `, r.Response.BufferString())
	} else {
		g.Log().Cat(consts.IR).Debug(ctx, "Return response ....")
	}
}
