package model

import (
	"context"
	"github.com/gogf/gf/v2/container/garray"
)

type Node struct {
	Key         string `json:"key"`
	Region      string `json:"region"`
	Weight      int    `json:"weight"`
	durationAry *garray.IntArray
}

func (n *Node) SetDuration(ctx context.Context, duration int) {
	if n.durationAry == nil {
		n.durationAry = garray.NewIntArray(true)
	}

	if n.durationAry.Len() >= 100 {
		n.durationAry.Clear()
	}

	n.durationAry.Append(duration)

}
func (n *Node) RecaculateWeight() {
	if (n.Weight - n.avgDuration()) <= 0 {

		n.Weight = 100

	} else {

		n.Weight -= n.avgDuration()
	}
}

func (n *Node) avgDuration() int {
	if n.durationAry == nil || n.durationAry.Len() == 0 {
		return 0
	}
	return n.durationAry.Sum() / n.durationAry.Len()
}
