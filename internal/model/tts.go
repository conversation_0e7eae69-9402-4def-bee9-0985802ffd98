package model

type TtsReq struct {
	Text            string  `json:"text"`
	Language        string  `json:"language"`
	Type            string  `json:"type"`
	Speed           float32 `json:"speed"`
	Volume          float32 `json:"volume"`
	UserId          string  `json:"user_id"`
	VccId           string  `json:"vccid"`
	RouteAccessCode string  `json:"route_access_code"`
	TextFormat      string  `json:"text_format"`
}

type TtsRes struct {
	Buf      []byte `json:"buf"`
	FileName string `json:"file_name"`
	Ext      string `json:"ext"`
}
