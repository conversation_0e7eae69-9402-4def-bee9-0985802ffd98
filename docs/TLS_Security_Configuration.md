# TLS 安全配置指南

## 概述

本文檔說明如何正確配置 SonaMesh 中的 TLS 設置，以確保與外部服務的安全連接。

## 問題背景

在之前的版本中，TLS 配置使用了硬編碼的 `InsecureSkipVerify: true`，這會跳過證書驗證，存在安全風險：

```go
// 不安全的配置（已修復）
c.TLSClientConfig = &tls.Config{
    InsecureSkipVerify: true,  // 跳過證書驗證
    MinVersion:         tls.VersionTLS12,
}
```

## 修復內容

### 1. 可配置的 TLS 設置

現在 TLS 設置可以通過配置文件控制：

```yaml
vendor:
  iii:
    STT:
      tls:
        insecure_skip_verify: false  # 預設為安全模式
        min_version: "1.2"          # TLS 最小版本
```

### 2. 支援的配置選項

#### `insecure_skip_verify`
- **類型**: boolean
- **預設值**: `false`
- **說明**: 是否跳過 TLS 證書驗證
- **建議**: 生產環境應設為 `false`

#### `min_version`
- **類型**: string
- **預設值**: `"1.2"`
- **可選值**: `"1.0"`, `"1.1"`, `"1.2"`, `"1.3"`
- **說明**: TLS 協議的最小版本
- **建議**: 使用 `"1.2"` 或更高版本

## 配置範例

### 生產環境配置（推薦）

```yaml
vendor:
  iii:
    STT:
      url: "wss://secure-server.com/ws"
      tls:
        insecure_skip_verify: false
        min_version: "1.2"
  
  Cyberon_default:
    TTS:
      http:
        url: "secure-grpc-server.com:443"
      tls:
        insecure_skip_verify: false
        min_version: "1.2"
```

### 開發環境配置（僅供開發使用）

```yaml
vendor:
  iii:
    STT:
      url: "wss://dev-server.local/ws"
      tls:
        insecure_skip_verify: true   # 僅開發環境
        min_version: "1.2"
```

## 安全建議

### 1. 生產環境
- ✅ 設置 `insecure_skip_verify: false`
- ✅ 使用有效的 SSL/TLS 證書
- ✅ 使用 TLS 1.2 或更高版本
- ✅ 定期更新證書

### 2. 開發環境
- ⚠️ 如需使用自簽名證書，可暫時設置 `insecure_skip_verify: true`
- ✅ 考慮配置本地 CA 證書
- ✅ 記錄並追蹤不安全的配置

### 3. 內部網路
- ✅ 即使在內部網路，也建議使用有效證書
- ⚠️ 如必須跳過驗證，請確保網路安全

## 遷移指南

### 從舊版本升級

1. **檢查現有配置**
   ```bash
   grep -r "InsecureSkipVerify" internal/
   ```

2. **更新配置文件**
   - 添加 `tls` 配置區塊
   - 設置適當的安全參數

3. **測試連接**
   - 確保服務能正常連接
   - 檢查 TLS 握手是否成功

### 故障排除

#### 證書驗證失敗
```
Error: x509: certificate signed by unknown authority
```
**解決方案**:
- 檢查證書是否有效
- 確認 CA 證書是否正確安裝
- 臨時可設置 `insecure_skip_verify: true`（僅開發環境）

#### TLS 版本不匹配
```
Error: tls: protocol version not supported
```
**解決方案**:
- 檢查服務器支援的 TLS 版本
- 調整 `min_version` 設置

## 影響的組件

以下組件已更新為支援可配置的 TLS 設置：

1. **III STT** (`internal/logic/iii/iii_stt.go`)
   - WebSocket 連接的 TLS 配置

2. **Cyberon TTS** (`internal/logic/cyberon/cyberon_tts.go`)
   - gRPC 連接的 TLS 配置

## 後續改進

計劃在未來版本中加入：
- 自定義 CA 證書路徑配置
- 客戶端證書認證支援
- TLS 連接狀態監控
- 證書到期提醒

## 相關文件

- [配置文件範例](../config/config.example.yaml)
- [Kubernetes ConfigMap](../manifest/deploy/kustomize/overlays/develop/configmap.yaml)
- [GoFrame 配置文檔](https://goframe.org/pages/viewpage.action?pageId=1114119)
