// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: service.proto

package cyberon

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TtsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string  `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty" dc:"必要，服務名稱"`                                             //必要，服務名稱
	Text        string  `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty" dc:"必要，合成文字"`                                                           //必要，合成文字
	Outfmt      string  `protobuf:"bytes,3,opt,name=outfmt,proto3" json:"outfmt,omitempty" dc:"輸出聲音格式，預設 mp3，支援 mp3, wav, pcm, pcm8(8K)"`                      //輸出聲音格式，預設 mp3，支援 mp3, wav, pcm, pcm8(8K)
	VbrQuality  int64   `protobuf:"varint,4,opt,name=vbr_quality,json=vbrQuality,proto3" json:"vbr_quality,omitempty" dc:"outfmt=mp3 時，音質設定，預設 4，0(好) ~ 9(差)"` //outfmt=mp3 時，音質設定，預設 4，0(好) ~ 9(差)
	Language    string  `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty" dc:"主要語言，預設：zh-TW"`                                             //主要語言，預設：zh-TW
	Phrbrk      bool    `protobuf:"varint,8,opt,name=phrbrk,proto3" json:"phrbrk,omitempty" dc:"是否自動斷詞，預設：false"`                                              //是否自動斷詞，預設：false
	Speaker     string  `protobuf:"bytes,10,opt,name=speaker,proto3" json:"speaker,omitempty" dc:"主要語言語者，預設 Sharon"`                                           //主要語言語者，預設 Sharon
	Speed       float32 `protobuf:"fixed32,12,opt,name=speed,proto3" json:"speed,omitempty" dc:"主要語言語速，預設 1，範圍 0.5 ~ 2"`                                       //主要語言語速，預設 1，範圍 0.5 ~ 2
	Gain        float32 `protobuf:"fixed32,14,opt,name=gain,proto3" json:"gain,omitempty" dc:"主要語言聲音大小，預設 1，範圍 0.5 ~ 4"`                                       //主要語言聲音大小，預設 1，範圍 0.5 ~ 4
	Token       string  `protobuf:"bytes,23,opt,name=token,proto3" json:"token,omitempty" dc:"必要，賽微提供"`                                                        //必要，賽微提供
	Uid         string  `protobuf:"bytes,24,opt,name=uid,proto3" json:"uid,omitempty" dc:"user unique id"`                                                     //user unique id
}

func (x *TtsRequest) Reset() {
	*x = TtsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsRequest) ProtoMessage() {}

func (x *TtsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsRequest.ProtoReflect.Descriptor instead.
func (*TtsRequest) Descriptor() ([]byte, []int) {
	return file_service_proto_rawDescGZIP(), []int{0}
}

func (x *TtsRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *TtsRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TtsRequest) GetOutfmt() string {
	if x != nil {
		return x.Outfmt
	}
	return ""
}

func (x *TtsRequest) GetVbrQuality() int64 {
	if x != nil {
		return x.VbrQuality
	}
	return 0
}

func (x *TtsRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *TtsRequest) GetPhrbrk() bool {
	if x != nil {
		return x.Phrbrk
	}
	return false
}

func (x *TtsRequest) GetSpeaker() string {
	if x != nil {
		return x.Speaker
	}
	return ""
}

func (x *TtsRequest) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *TtsRequest) GetGain() float32 {
	if x != nil {
		return x.Gain
	}
	return 0
}

func (x *TtsRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *TtsRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type TtsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TtsResponse) Reset() {
	*x = TtsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TtsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TtsResponse) ProtoMessage() {}

func (x *TtsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TtsResponse.ProtoReflect.Descriptor instead.
func (*TtsResponse) Descriptor() ([]byte, []int) {
	return file_service_proto_rawDescGZIP(), []int{1}
}

func (x *TtsResponse) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_service_proto protoreflect.FileDescriptor

var file_service_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0d, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x9b,
	0x02, 0x0a, 0x0a, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x66, 0x6d, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x66, 0x6d, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x62, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x76, 0x62, 0x72, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x68, 0x72, 0x62,
	0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x68, 0x72, 0x62, 0x72, 0x6b,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x70, 0x65, 0x61, 0x6b, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x67, 0x61, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04,
	0x67, 0x61, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x21, 0x0a, 0x0b,
	0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32,
	0x51, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x40, 0x0a, 0x03, 0x54, 0x54, 0x53, 0x12, 0x19, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x54, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x30, 0x01, 0x42, 0x0a, 0x5a, 0x08, 0x2f, 0x63, 0x79, 0x62, 0x65, 0x72, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_service_proto_rawDescOnce sync.Once
	file_service_proto_rawDescData = file_service_proto_rawDesc
)

func file_service_proto_rawDescGZIP() []byte {
	file_service_proto_rawDescOnce.Do(func() {
		file_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_service_proto_rawDescData)
	})
	return file_service_proto_rawDescData
}

var file_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_service_proto_goTypes = []interface{}{
	(*TtsRequest)(nil),  // 0: streamservice.TtsRequest
	(*TtsResponse)(nil), // 1: streamservice.TtsResponse
}
var file_service_proto_depIdxs = []int32{
	0, // 0: streamservice.StreamService.TTS:input_type -> streamservice.TtsRequest
	1, // 1: streamservice.StreamService.TTS:output_type -> streamservice.TtsResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_service_proto_init() }
func file_service_proto_init() {
	if File_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TtsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_service_proto_goTypes,
		DependencyIndexes: file_service_proto_depIdxs,
		MessageInfos:      file_service_proto_msgTypes,
	}.Build()
	File_service_proto = out.File
	file_service_proto_rawDesc = nil
	file_service_proto_goTypes = nil
	file_service_proto_depIdxs = nil
}
