apiVersion: v1
kind: ConfigMap
metadata:
  name: template-single-configmap
data:
  config.yaml: |
    server:
      address:     ":8000"
      openapiPath: "/api.json"
      swaggerPath: "/swagger"

    logger:
      level : "all"
      stdout: true

    # WebSocket 配置
    websocket:
      hand_shake_timeout: "45s"

    # 廠商配置 - 安全的 TLS 設置
    vendor:
      # III Bot 配置
      iii:
        STT:
          # 請替換為實際的服務 URL
          url: "wss://your-iii-stt-server.com/ws"
          http_url: "https://your-iii-stt-server.com/api"
          # TLS 安全配置
          tls:
            # 生產環境建議設為 false
            insecure_skip_verify: false
            min_version: "1.2"
        TTS:
          url: "https://your-iii-tts-server.com/api"
          tls:
            insecure_skip_verify: false
            min_version: "1.2"

      # Cyberon 配置
      Cyberon_default:
        STT:
          ws:
            url: "wss://your-cyberon-stt-server.com/ws"
          tls:
            insecure_skip_verify: false
            min_version: "1.2"
        TTS:
          http:
            url: "your-cyberon-tts-server.com:443"
          tls:
            insecure_skip_verify: false
            min_version: "1.2"

      # EmotiBot 配置
      EmotiBot_default:
        STT:
          ws:
            url: "wss://your-emotibot-stt-server.com/ws"
          http:
            url: "https://your-emotibot-stt-server.com/api"
          tls:
            insecure_skip_verify: false
            min_version: "1.2"
        TTS:
          http:
            url: "https://your-emotibot-tts-server.com/api"
          tls:
            insecure_skip_verify: false
            min_version: "1.2"
