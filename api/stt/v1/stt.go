package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type SttReq struct {
	g.Meta          `path:"/v1/stt/file" mime:"multipart/form-data" tags:"STT" method:"post" summary:"recognize audio file"`
	File            *ghttp.UploadFile `json:"file" type:"file" v:"required"`
	WebHook         string            `json:"web_hook"`
	IsWaitResult    bool              `json:"is_wait_result"`
	VccId           string            `json:"vccid" v:"required"`
	RouteAccessCode string            `json:"route_access_code"`
	ResourceKey     string            `json:"resource_key"`
	EndPointID      string            `json:"end_point_id"`
	Region          string            `json:"region"`
	Translate       bool              `json:"translate"`
}

type SttRes struct {
	g.Meta  `mime:"application/json"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Result  string `json:"result"`
}
