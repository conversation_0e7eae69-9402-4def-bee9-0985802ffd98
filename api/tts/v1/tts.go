package v1

import "github.com/gogf/gf/v2/frame/g"

type TtsReq struct {
	g.Meta          `path:"/v1/tts/" method:"post" tags:"TTS" summary:"Synthesis" `
	Text            string  `json:"text" v:"required" dc:"text to be synthesized"`
	Language        string  `json:"language" dc:"Converted language"`
	Type            string  `json:"type" dc:"audio file type wav mp3 pcm"`
	Speed           float32 `json:"speed" dc:"speaking speed"`
	Volume          float32 `json:"volume" dc:"volume of sound"`
	UserId          string  `json:"user_id" dc:"user‘s unique id"`
	VccId           string  `json:"vccid" v:"required" dc:"tenant's unique id"`
	RouteAccessCode string  `json:"route_access_code" dc:"routing code"`
	TextFormat      string  `json:"text_format" dc:"text format default is text"`
}

type TtsRes struct{}

type DownloadVoiceFileReq struct {
	g.Meta   `path:"/v1/tts/download_voice_file" method:"get" tags:"TTS" summary:"DownloadVoiceFile" `
	FileName string `json:"file_name"`
}

type DownloadVoiceFileRes struct{}
