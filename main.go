package main

import (
	"SonaMesh/internal/consts"
	_ "SonaMesh/internal/packed"
	context2 "context"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gbuild"
	"github.com/gogf/gf/v2/os/glog"
	"golang.org/x/net/context"

	_ "SonaMesh/internal/logic"

	"github.com/gogf/gf/v2/os/gctx"

	"SonaMesh/internal/cmd"
)

func main() {

	g.Log().SetHeaderPrint(false)
	buildInfo := gbuild.Info()

	g.Log().Cat(consts.INFO).Infof(context.TODO(), "STMesh Version: %s , Build Time: %s", buildInfo.Version, buildInfo.Time)
	g.Log().SetFlags(glog.F_FILE_SHORT | glog.F_TIME_STD | glog.F_CALLER_FN)

	g.Log().SetHeaderPrint(true)
	g.Try<PERSON>atch(
		gctx.GetInitCtx(),
		func(ctx context2.Context) {
			cmd.Main.Run(gctx.GetInitCtx())

		},
		func(ctx context2.Context, exception error) {
			g.Log().Error(ctx, exception)
		},
	)
}
