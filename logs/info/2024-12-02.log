STMesh Version:  , Build Time: 
{18df3d0b964a0d189c1e2352bfbf8096} Load into cache...
STMesh Version:  , Build Time: 
{2091ca24cc4a0d184603b467441cae5e} Load into cache...
2024-12-02 15:14:58.111 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:63356 connected ...
2024-12-02 15:14:58.111 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:63355 connected ...
2024-12-02 15:14:58.112 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvq2z0d6112rfrzffs2007qs2he","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:14:58.111 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:63354 connected ...
2024-12-02 15:14:58.112 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvq2z0d6112rfryt28100wcnm8z","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:14:58.112 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvq2z0d6112rfrzffs2007qs2he",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:14:58.112 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvq2z0d6112rfrzg7k300a911bu","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:14:58.112 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvq2z0d6112rfryt28100wcnm8z",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:14:58.112 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvq2z0d6112rfrzg7k300a911bu",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:14:58.113 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:14:58.113 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:14:58.113 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:14:58.114 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": -1
}
2024-12-02 15:14:58.114 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": -1
}
2024-12-02 15:14:58.115 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": -1
}
2024-12-02 15:17:54.030 [INFO] {b8ca8c0ff84a0d181131c571c39683e4} Load into cache...
STMesh Version:  , Build Time: 
2024-12-02 15:18:15.733 [INFO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:63726 connected ...
2024-12-02 15:18:15.734 [INFO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqc70d6115a831wzk100gg0eda","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:18:15.734 [INFO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqc70d6115a831wzk100gg0eda",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:18:15.734 [INFO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
STMesh Version:  , Build Time: 
{308e9fcf204b0d18c38a7133db2aa512} Load into cache...
2024-12-02 15:20:39.778 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:63993 connected ...
2024-12-02 15:20:39.779 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqjk0d61174ebqet4100moe3h2","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:20:39.779 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqjk0d61174ebqet4100moe3h2",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:20:39.779 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:20:39.789 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:20:44.836 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:20:53.475 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
STMesh Version:  , Build Time: 
{500b087c284b0d184203ed3bc1c2f990} Load into cache...
2024-12-02 15:21:15.543 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64045 connected ...
2024-12-02 15:21:15.543 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqmg0d6117ktt2j5s100qpopa0","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:21:15.543 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqmg0d6117ktt2j5s100qpopa0",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:21:15.543 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:21:15.546 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:21:20.600 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
STMesh Version:  , Build Time: 
{e0f6783f504b0d18549df5479840c1bc} Load into cache...
2024-12-02 15:24:07.700 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64331 connected ...
2024-12-02 15:24:07.700 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqto0d6119rwyro4w100au24dn","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:24:07.700 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqto0d6119rwyro4w100au24dn",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:24:07.701 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:24:07.711 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:24:18.837 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64392 connected ...
2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqv00d611a2xnbxpc100xk0ony","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqv00d611a2xnbxpc100xk0ony",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:24:31.687 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:24:36.726 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:24:45.367 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:24:49.641 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:24:50.032 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢，你要登入那個融。"
	},
	"status": 0
}
2024-12-02 15:24:50.032 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:347: Interrupting ... 
2024-12-02 15:25:15.642 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:25:15.851 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個，我想問說他，我剛好。"
	},
	"status": 0
}
2024-12-02 15:25:15.851 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:347: Interrupting ... 
2024-12-02 15:25:33.487 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64483 connected ...
2024-12-02 15:25:33.487 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64484 connected ...
2024-12-02 15:25:33.487 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64482 connected ...
2024-12-02 15:25:33.487 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqy90d611avbq4520200xlv4os","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:25:33.487 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqy90d611avbq45201006lgkmt","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:25:33.487 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvqy90d611avbq528g3006pri6c","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:25:33.488 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqy90d611avbq45201006lgkmt",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:25:33.488 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqy90d611avbq528g3006pri6c",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:25:33.487 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvqy90d611avbq4520200xlv4os",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:25:33.489 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:25:33.489 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:25:33.489 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:25:33.491 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:33.491 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:33.491 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:39.537 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:25:44.574 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:25:44.574 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:25:47.645 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:25:58.542 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:25:58.543 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:26:01.535 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:26:01.544 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:26:05.327 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:26:08.318 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:26:08.318 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:26:26.646 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是差一天，為什麼你們門市就不肯讓我折抵啊？而且我這邊還買很多杯，買十幾杯。嗯，小姐，不好意思，因為這不是差差幾天，而是因為他就是過期門市，就沒辦法收券了。"
	},
	"status": 0
}
2024-12-02 15:26:34.344 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:26:34.588 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
STMesh Version:  , Build Time: 
{c0268ce9784b0d1812e77b459aac86d7} Load into cache...
STMesh Version:  , Build Time: 
{2038e4578c4b0d180ff4a4693d81958b} Load into cache...
2024-12-02 15:28:22.876 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64789 connected ...
2024-12-02 15:28:22.876 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64790 connected ...
2024-12-02 15:28:22.876 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:64791 connected ...
2024-12-02 15:28:22.877 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvr7z0d611d154bjkg100uc217k","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:28:22.877 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvr7z0d611d154c4eg300h7vxll","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:28:22.877 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvr7z0d611d154c3mo20070hznm","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:28:22.877 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvr7z0d611d154c4eg300h7vxll",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:28:22.877 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvr7z0d611d154bjkg100uc217k",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:28:22.877 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:28:22.877 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:28:22.877 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvr7z0d611d154c3mo20070hznm",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:28:22.877 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:28:22.881 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:22.881 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:22.881 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:28.672 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:28:29.126 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:28:34.133 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:28:36.749 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:28:37.605 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:28:48.114 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:28:51.041 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:28:51.634 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:28:54.877 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:28:57.894 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:29:16.236 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是差一天，為什麼你們門市就不肯讓我折抵啊？而且我這邊還買很多杯，買十幾杯。嗯，小姐，不好意思，因為這不是差差幾天，而是因為他就是過期門市，就沒辦法收券了。"
	},
	"status": 0
}
2024-12-02 15:29:20.189 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:29:23.659 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:29:23.849 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:29:33.399 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:29:34.155 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "啊，我有點忘記了抱歉，因為中間我我我今年去住了兩次院，有點記憶力，有點不太好，對，OK，所以你就是收到那個通知。"
	},
	"status": 0
}
2024-12-02 15:29:34.352 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那這有沒有什麼方式可以這個補我們啦，因為這個很多張欸，我有很多張欸，不是一張而已欸，我了解，但是因為它是一整年度的兌換，所以這個卷的部分它沒有辦法再補位。"
	},
	"status": 0
}
2024-12-02 15:29:48.487 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我，我一直奇怪啊，這是什麼東西我一直想不起來啊。你是哪一些銀行生貸款的？呃，臺灣銀行啊分行呢呃中心分行。"
	},
	"status": 0
}
2024-12-02 15:29:49.306 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那有沒有什麼方式可以理補我們的這種損失啊？因為那個很貴耶小姐，不好意思，因為這個券現在這個年曆的附加贈品，所以它的確沒有辦法，只要只要過期了，就沒辦法再做使用了。"
	},
	"status": 0
}
2024-12-02 15:29:56.704 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "臺銀的中心中心新村分行嗎？對對對，中興新村好，所以你忘記拿收貨袋，反正你就是收到那個平臺。"
	},
	"status": 0
}
2024-12-02 15:29:58.682 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "差一天欸，對不好意思，這個真的沒辦法使用，可以幫我跟你們總公司在噴印嗎？"
	},
	"status": 0
}
2024-12-02 15:30:03.852 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:30:04.472 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:30:05.050 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
2024-12-02 15:30:12.882 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "嗯，小姐，這個部分我們是確定他，因為往年就算就包含我們今年度的也是一樣，都是1月1號啟用到12月30要把它使用完畢，所以過期了，它確實就沒有辦法再做使用了。"
	},
	"status": 0
}
2024-12-02 15:30:22.818 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "就是通知的簡訊，那先生，那我就是請你要到平台去登入這個資料啊。他說，5月31號填要登入啊，你現在如果說可以的話，你就現在登錄，因為你會忘我用好你，這樣叫我用好不好你，你就是照那個平臺裡面之後啊，那手機點就可以了，是不是如果你手機可以用的話哦，可以來我來，我現在我現在我來。"
	},
	"status": 0
}
2024-12-02 15:30:28.895 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "可是你們這樣很很就是有點不合乎情理，就是說一般是31號，只有你們寫的是30號，誰會知道是這是差這個一天啊，為什麼你們要這樣子訂呢？"
	},
	"status": 0
}
2024-12-02 15:30:29.548 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "然後我第一次登入是按第一次還是我們對對第一次登入對好了，那輸入你的桶編。"
	},
	"status": 0
}
2024-12-02 15:30:35.217 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "然後他會傳驗證碼到你的手機。"
	},
	"status": 0
}
2024-12-02 15:30:41.653 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我覺得這樣你要補給我們消費者才對呀。小姐，不好意思欸，這個真的沒有辦法啦。這個真的不好意思，因為它是整年度的兌換。"
	},
	"status": 0
}
2024-12-02 15:30:46.075 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "他，他是整年度的喜歡不好意思。"
	},
	"status": 0
}
2024-12-02 15:30:46.625 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好傳一次馬到我手機，你已經登錄過了，基本上前往你登入頁面好，那你那你就是回到我已經登入過那個案件。"
	},
	"status": 0
}
2024-12-02 15:30:49.886 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "可以再幫我fine。"
	},
	"status": 0
}
2024-12-02 15:30:51.540 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "欸，他現在跳說。"
	},
	"status": 0
}
2024-12-02 15:30:54.241 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哼嗯。"
	},
	"status": 0
}
2024-12-02 15:30:57.499 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這個我真的可以跟小姐說明，我們沒有辦法再補，或是就是提供特殊處理啦。這個真的不好意思，因為它已經過期了。"
	},
	"status": 0
}
2024-12-02 15:31:03.384 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "大家要我輸入那個呢？同邊跟密碼咧啊密碼你記得嗎？我希望看好不好，嗯。"
	},
	"status": 0
}
STMesh Version:  , Build Time: 
{40344588014c0d18f46fa90b22d5e7fb} Load into cache...
2024-12-02 15:36:45.805 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:49542 connected ...
2024-12-02 15:36:45.805 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:49544 connected ...
2024-12-02 15:36:45.805 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:49543 connected ...
2024-12-02 15:36:45.806 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvrta0d611jg6nb8ds100wpqsi4","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:36:45.806 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvrta0d611jg6ncqe8300t9cot1","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:36:45.806 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvrta0d611jg6nc2h4200p8xesg","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:36:45.806 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvrta0d611jg6nb8ds100wpqsi4",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:36:45.806 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvrta0d611jg6nc2h4200p8xesg",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:36:45.806 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:36:45.806 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvrta0d611jg6ncqe8300t9cot1",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:36:45.806 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:36:45.806 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:36:45.816 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:45.816 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:45.816 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:50.864 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:36:51.008 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:36:56.887 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:36:59.493 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:36:59.626 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:37:10.882 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:37:13.845 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:37:13.847 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:37:13.914 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:37:20.620 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:37:43.028 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:37:43.070 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:37:46.401 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:37:46.550 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:37:46.613 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:37:56.470 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:37:56.756 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:37:56.901 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "啊，我有點忘記了抱歉，因為中間我我我今年去住了兩次院，有點記憶力，有點不太好，對，OK，所以你就是收到那個通知。"
	},
	"status": 0
}
2024-12-02 15:38:11.227 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我，我一直奇怪啊，這是什麼東西我一直想不起來啊。你是哪一些銀行生貸款的？呃，臺灣銀行啊分行呢呃中心分行。"
	},
	"status": 0
}
2024-12-02 15:38:19.428 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "臺銀的中心中心新村分行嗎？對對對，中興新村好，所以你忘記拿收貨袋，反正你就是收到那個平臺。"
	},
	"status": 0
}
2024-12-02 15:38:26.774 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:38:26.774 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:38:27.306 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:38:27.312 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:38:27.420 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
2024-12-02 15:38:27.567 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
STMesh Version:  , Build Time: 
{009bbc403c4c0d18a7151e7509ee03ab} Load into cache...
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:50112 connected ...
2024-12-02 15:40:56.792 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:50110 connected ...
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:38: Client [::1]:50111 connected ...
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvs470d611mnhi2po81006foo45","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:40:56.792 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvs470d611mnhi3f4w200rfmz2o","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"1arspqvs470d611mnhi3rhc300cnf85c","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvs470d611mnhi2po81006foo45",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvs470d611mnhi3rhc300cnf85c",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:40:56.792 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:197: Speech to text - start: {
	"action": "start",
	"call_id": "1arspqvs470d611mnhi3f4w200rfmz2o",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:40:56.792 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-12-02 15:40:56.802 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:40:56.802 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:40:56.803 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:121: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:41:01.884 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:41:02.998 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:41:07.897 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:41:10.580 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:41:11.107 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:41:21.843 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:41:24.812 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:41:24.852 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:41:28.766 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:41:31.629 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:164: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
