2024-01-31 13:40:02.424 [INFO] {d0d1972a0958af176210a43ce83bc07c} Load into cache...
STMesh Version:  , Build Time: 
2024-01-31 13:43:19.161 [INFO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这个是一个测试的tts 文字",
	"type": "wav",
	"user_id": "123456",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 13:43:19.162 [INFO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这个是一个测试的tts 文字",
	"type": "wav",
	"user_id": "123456",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 13:43:19.162 [INFO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-31 14:31:22.652 [INFO] {c88a215dda5aaf177826461b79313019} Load into cache...
STMesh Version:  , Build Time: 
2024-01-31 14:31:50.894 [INFO] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "audio_01.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"audio_01.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 1646760
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-31 14:31:50.896 [INFO] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/audio_01.wav with vccid:111111 route access code:000
2024-01-31 14:31:50.896 [INFO] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-31 14:39:00.373 [INFO] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "audio_01.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"audio_01.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 1646760
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-31 14:39:00.375 [INFO] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/audio_01.wav with vccid:111111 route access code:000
2024-01-31 14:39:00.376 [INFO] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-31 14:42:58.010 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62689 connected ...
2024-01-31 14:42:58.011 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"9wbozm01kyecysork2gc7vc100m4nttl","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-01-31 14:42:58.012 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:162: Speech to text - start: {
	"action": "start",
	"call_id": "9wbozm01kyecysork2gc7vc100m4nttl",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-01-31 14:42:58.013 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-31 14:42:58.257 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:96: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-01-31 14:42:58.274 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.275 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.275 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.275 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.276 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.276 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.277 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.277 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.277 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.279 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.279 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.279 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.287 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.287 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.288 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.288 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.288 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.292 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.292 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.292 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.293 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.293 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.293 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.294 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.294 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.294 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.296 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.296 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.296 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.298 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.298 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.298 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.301 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.301 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.301 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.301 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.302 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.302 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.303 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.303 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.303 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.304 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.304 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.304 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.305 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.305 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.305 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.305 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.306 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.306 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.306 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.306 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.307 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.307 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.307 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.308 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.308 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.308 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.308 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.309 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.309 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.309 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.309 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.309 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.310 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.310 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.310 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.311 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.312 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.312 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.312 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.313 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.313 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.313 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.313 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.314 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.314 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.314 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.314 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.315 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.315 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.315 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.316 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.316 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.316 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.317 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.318 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.319 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.319 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.319 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.319 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.319 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.320 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.320 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.320 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.321 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.321 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.321 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.321 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.321 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.322 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.322 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.322 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.324 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.324 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.324 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.324 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.325 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.325 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.325 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.325 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.325 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.326 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.326 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.326 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.327 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.327 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.327 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.369 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.370 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.370 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.372 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:96: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -1
}
2024-01-31 14:42:58.429 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.429 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.429 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.429 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.429 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.430 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.430 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.430 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.433 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.433 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.433 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.434 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.436 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
2024-01-31 14:42:58.439 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:213: Send voice buffer ...
STMesh Version:  , Build Time: 
2024-01-31 16:38:21.962 [INFO] {e08b9d98c661af17942b336ef8776c4b} Load into cache...
2024-01-31 16:39:03.376 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试",
	"type": "wav",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 16:39:03.377 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试",
	"type": "wav",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 16:39:03.377 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-31 16:39:04.288 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is wav 
2024-01-31 16:39:15.865 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试文字",
	"type": "mp3",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 16:39:15.866 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试文字",
	"type": "mp3",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 16:39:15.867 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-31 16:39:16.679 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is mp3 
