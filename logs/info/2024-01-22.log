SonaMesh Version:  , Build Time: 
2024-01-22 10:48:28.290 [INFO] {583aebde768bac172881a8758882d57d} Load into cache...
2024-01-22 10:48:31.195 [INFO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:48:31.195 [INFO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:48:31.196 [INFO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
SonaMesh Version:  , Build Time: 
2024-01-22 10:51:38.580 [INFO] {583aebde768bac172881a8758882d57d} Load into cache...
2024-01-22 10:51:41.221 [INFO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:51:41.221 [INFO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:51:41.221 [INFO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-22 10:53:42.908 [INFO] {583aebde768bac172881a8758882d57d} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 10:54:29.541 [INFO] {d8effb8dcc8bac1718067372bf91c38e} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 10:54:32.113 [INFO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:54:32.113 [INFO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:54:32.114 [INFO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-22 10:55:13.742 [INFO] {086197d8d68bac1762ed1901b958af74} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 10:55:30.021 [INFO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:55:30.021 [INFO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:55:30.021 [INFO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-22 11:05:48.373 [INFO] {c06cbf77698cac17e1760d64e694aa32} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 11:05:51.808 [INFO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:05:51.809 [INFO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:05:51.810 [INFO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
SonaMesh Version:  , Build Time: 
2024-01-22 11:06:54.647 [INFO] {c06cbf77698cac17e1760d64e694aa32} Load into cache...
2024-01-22 11:06:57.550 [INFO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:06:57.550 [INFO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:06:57.552 [INFO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-22 11:09:07.814 [INFO] {c06cbf77698cac17e1760d64e694aa32} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 11:09:25.147 [INFO] {083c41149d8cac17e258fa5f07ed72bc} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-22 11:09:34.141 [INFO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:09:34.142 [INFO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:09:34.143 [INFO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
