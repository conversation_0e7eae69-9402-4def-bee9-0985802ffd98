2024-01-16 14:25:08.899 [INFO] {b8dec4bbcfbfaa178193a37d746e2e6b} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 14:25:25.230 [INFO] {e88791c6d3bfaa17661eb113bc0f41d4} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "short.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"short.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 523138
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 14:25:25.231 [INFO] {e88791c6d3bfaa17661eb113bc0f41d4} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/short.wav with vccid:111111 route access code:000
2024-01-16 14:25:25.233 [INFO] {e88791c6d3bfaa17661eb113bc0f41d4} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 14:26:26.700 [INFO] {a8e635e6e1bfaa17aaed8f3d96e140b8} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 14:26:34.923 [INFO] {50e1ee00e4bfaa173f47130130aec776} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "short.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"short.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 523138
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 14:26:34.925 [INFO] {50e1ee00e4bfaa173f47130130aec776} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/short.wav with vccid:111111 route access code:000
2024-01-16 14:26:34.925 [INFO] {50e1ee00e4bfaa173f47130130aec776} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
SonaMesh Version:  , Build Time: 
2024-01-16 14:29:38.566 [INFO] {a8e635e6e1bfaa17aaed8f3d96e140b8} Load into cache...
2024-01-16 14:30:20.521 [INFO] {a8e635e6e1bfaa17aaed8f3d96e140b8} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 14:30:29.069 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 14:36:27.571 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 14:36:41.902 [INFO] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "a2_01.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"a2_01.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 17448880
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 14:36:41.917 [INFO] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/a2_01.wav with vccid:111111 route access code:000
2024-01-16 14:36:41.917 [INFO] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:06:27.585 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} [SonaMesh/internal/logic/tts.updateRecordFromCache] tts.go:71: Update record from cache...
2024-01-16 15:06:27.598 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:108: Check voice files  expired...
2024-01-16 15:13:56.285 [INFO] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 76608
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:13:56.286 [INFO] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.mp3 with vccid:111111 route access code:000
2024-01-16 15:13:56.286 [INFO] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:18:05.097 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 15:18:15.967 [INFO] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 76608
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:18:15.969 [INFO] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.mp3 with vccid:111111 route access code:000
2024-01-16 15:18:15.969 [INFO] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:20:01.388 [INFO] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 226244
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:20:01.390 [INFO] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.wav with vccid:111111 route access code:000
2024-01-16 15:20:01.390 [INFO] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:20:48.901 [INFO] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 226244
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:20:48.902 [INFO] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.wav with vccid:111111 route access code:000
2024-01-16 15:20:48.902 [INFO] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:22:07.281 [INFO] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (1).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (1).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 75444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:22:07.282 [INFO] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (1).wav with vccid:111111 route access code:000
2024-01-16 15:22:07.282 [INFO] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:22:59.484 [INFO] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (1).mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (1).mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 114048
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:22:59.485 [INFO] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (1).mp3 with vccid:111111 route access code:000
2024-01-16 15:22:59.486 [INFO] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:23:14.293 [INFO] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (1).mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (1).mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 114048
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:23:14.295 [INFO] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (1).mp3 with vccid:111111 route access code:000
2024-01-16 15:23:14.296 [INFO] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:26:53.787 [INFO] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (2).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (2).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 452444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:26:53.789 [INFO] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (2).wav with vccid:111111 route access code:000
2024-01-16 15:26:53.789 [INFO] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:27:13.024 [INFO] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (2).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (2).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 452444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:27:13.027 [INFO] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (2).wav with vccid:111111 route access code:000
2024-01-16 15:27:13.032 [INFO] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:27:43.953 [INFO] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (2).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (2).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 452444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:27:43.953 [INFO] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (2).wav with vccid:111111 route access code:000
2024-01-16 15:27:43.953 [INFO] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:31:15.589 [INFO] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 226244
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:31:15.590 [INFO] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.wav with vccid:111111 route access code:000
2024-01-16 15:31:15.590 [INFO] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 15:34:56.305 [INFO] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 226244
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 15:34:56.306 [INFO] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.wav with vccid:111111 route access code:000
2024-01-16 15:34:56.306 [INFO] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:01:45.067 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:04:17.240 [INFO] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 226244
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:04:17.241 [INFO] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.wav with vccid:111111 route access code:000
2024-01-16 16:04:17.241 [INFO] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:06:48.660 [INFO] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (1).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (1).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 75444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:06:48.663 [INFO] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (1).wav with vccid:111111 route access code:000
2024-01-16 16:06:48.663 [INFO] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:07:15.464 [INFO] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test (1).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test (1).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 75444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:07:15.465 [INFO] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test (1).wav with vccid:111111 route access code:000
2024-01-16 16:07:15.465 [INFO] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:07:45.871 [INFO] {a07cb58269c5aa177e5c761faa333299} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 76608
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:07:45.872 [INFO] {a07cb58269c5aa177e5c761faa333299} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.mp3 with vccid:111111 route access code:000
2024-01-16 16:07:45.872 [INFO] {a07cb58269c5aa177e5c761faa333299} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:09:29.388 [INFO] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 150844
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:09:29.390 [INFO] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech.wav with vccid:111111 route access code:000
2024-01-16 16:09:29.390 [INFO] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:10:14.466 [INFO] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 150844
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:10:14.469 [INFO] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech.wav with vccid:111111 route access code:000
2024-01-16 16:10:14.469 [INFO] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:13:00.656 [INFO] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 150844
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:13:00.659 [INFO] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech.wav with vccid:111111 route access code:000
2024-01-16 16:13:00.659 [INFO] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:17:15.730 [INFO] {88aa8030eec5aa17825c761f6f6e0efd} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio Test.mp3",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio Test.mp3\""
			],
			"Content-Type": [
				"audio/mpeg"
			]
		},
		"Size": 76608
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:17:15.732 [INFO] {88aa8030eec5aa17825c761f6f6e0efd} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.mp3 with vccid:111111 route access code:000
2024-01-16 16:17:15.732 [INFO] {88aa8030eec5aa17825c761f6f6e0efd} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:21:53.397 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:21:55.305 [INFO] {3857695b1ac0aa1751d0345a71fc06f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:22:56.255 [INFO] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio.wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio.wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 150844
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:22:56.257 [INFO] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio.wav with vccid:111111 route access code:000
2024-01-16 16:22:56.257 [INFO] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:23:58.528 [INFO] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile] stt_v1_stt.go:32: Recognize audio file:{
	"file": {
		"Filename": "Speech Studio (1).wav",
		"Header": {
			"Content-Disposition": [
				"form-data; name=\"file\"; filename=\"Speech Studio (1).wav\""
			],
			"Content-Type": [
				"audio/x-wav"
			]
		},
		"Size": 75444
	},
	"route_access_code": "000",
	"vccid": "111111",
	"web_hook": "http://localhost:9000"
}
2024-01-16 16:23:58.529 [INFO] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile] stt.go:283: Recognize file /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio (1).wav with vccid:111111 route access code:000
2024-01-16 16:23:58.530 [INFO] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:59: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:36:17.319 [INFO] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:17.321 [INFO] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:17.324 [INFO] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:36:19.009 [INFO] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is wav 
2024-01-16 16:36:40.010 [INFO] {28361245fdc6aa1774bf16798c000488} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:40.010 [INFO] {28361245fdc6aa1774bf16798c000488} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:53.150 [INFO] {784f181400c7aa177fedd0620c34bfa1} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:36:56.745 [INFO] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:56.745 [INFO] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:36:56.745 [INFO] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:36:57.700 [INFO] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is mp3 
2024-01-16 16:37:17.897 [INFO] {70cc52eb05c7aa170c6e94472e4ca3e0} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:37:21.049 [INFO] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "pcm",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:37:21.049 [INFO] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": " 这是一段tts的测试文本",
	"type": "pcm",
	"user_id": "",
	"vccid": "111111",
	"volume": 3
}
2024-01-16 16:37:21.049 [INFO] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:37:21.684 [INFO] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is pcm 
2024-01-16 16:39:42.842 [INFO] {d0c64b8f27c7aa177754b815883ae8a4} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:40:26.186 [INFO] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1
}
2024-01-16 16:40:26.187 [INFO] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1
}
2024-01-16 16:40:26.189 [INFO] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:49:11.351 [INFO] {88079febabc7aa17ebd87a1100b7a0b1} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:49:13.192 [INFO] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1
}
2024-01-16 16:49:13.192 [INFO] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1
}
2024-01-16 16:49:13.194 [INFO] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:49:29.752 [INFO] {a8c92745b0c7aa179588b436e38ea4c9} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:49:31.494 [INFO] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:31.494 [INFO] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:31.494 [INFO] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:49:45.571 [INFO] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1.1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:45.572 [INFO] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1.1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:45.573 [INFO] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:49:53.464 [INFO] {3877f7beb5c7aa17c5fd282af2584361} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:49:56.618 [INFO] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1.1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:56.618 [INFO] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1.1,
	"text": " 这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:49:56.619 [INFO] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:50:20.452 [INFO] {e0fab613bcc7aa17ff891740d4e691bf} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:50:29.151 [INFO] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 2,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:50:29.151 [INFO] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 2,
	"text": " 这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:50:29.151 [INFO] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 16:50:59.373 [INFO] {48be6530c5c7aa17df5670596b0fa7f5} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-16 16:51:01.559 [INFO] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 2,
	"text": " 这是一段tts的测试文本",
	"type": "pcm",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:51:01.559 [INFO] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 2,
	"text": " 这是一段tts的测试文本",
	"type": "pcm",
	"user_id": "",
	"vccid": "111111",
	"volume": 100
}
2024-01-16 16:51:01.560 [INFO] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-16 17:20:59.414 [INFO] {48be6530c5c7aa17df5670596b0fa7f5} [SonaMesh/internal/logic/tts.updateRecordFromCache] tts.go:71: Update record from cache...
2024-01-16 17:20:59.462 [INFO] {48be6530c5c7aa17df5670596b0fa7f5} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:108: Check voice files  expired...
2024-01-16 17:50:59.385 [INFO] {48be6530c5c7aa17df5670596b0fa7f5} [SonaMesh/internal/logic/tts.updateRecordFromCache] tts.go:71: Update record from cache...
2024-01-16 17:50:59.397 [INFO] {48be6530c5c7aa17df5670596b0fa7f5} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:108: Check voice files  expired...
