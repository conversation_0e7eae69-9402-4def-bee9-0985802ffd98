SonaMesh Version:  , Build Time: 
2024-01-19 11:37:53.360 [INFO] {18ab2ae16ba2ab17d6410a69c69ecb25} Load into cache...
2024-01-19 11:39:15.717 [INFO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:39:15.717 [INFO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:39:15.723 [INFO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 11:41:21.833 [INFO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:41:21.833 [INFO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:41:21.834 [INFO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 11:42:36.189 [INFO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:42:36.189 [INFO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:42:36.190 [INFO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 11:43:02.862 [INFO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:02.862 [INFO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:02.862 [INFO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 11:43:37.694 [INFO] {888e5e4fbca2ab17adab9176c3725943} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 11:43:41.632 [INFO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:41.632 [INFO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:41.633 [INFO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 12:02:00.567 [INFO] {206342eebca3ab17d79878248ebcd54f} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 12:04:01.525 [INFO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个cyberon tts的测试",
	"type": "wav",
	"user_id": "111",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 12:04:01.526 [INFO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个cyberon tts的测试",
	"type": "wav",
	"user_id": "111",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 12:04:01.528 [INFO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 12:36:28.064 [INFO] {206342eebca3ab17d79878248ebcd54f} [SonaMesh/internal/logic/tts.updateRecordFromCache] tts.go:71: Update record from cache...
2024-01-19 12:36:28.072 [INFO] {206342eebca3ab17d79878248ebcd54f} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:108: Check voice files  expired...
2024-01-19 13:48:27.109 [INFO] {88eb58ea8ba9ab171d45445ba3ebd0b8} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 13:49:00.956 [INFO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 13:49:00.956 [INFO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 13:49:00.957 [INFO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 13:49:25.348 [INFO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 13:49:25.349 [INFO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 13:49:25.352 [INFO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 13:50:18.053 [INFO] {18ee7a8ea6a9ab17959cbf3274e114c9} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 14:19:37.400 [INFO] {b89a9c7f40abab1793d9d403aa580eb1} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 14:20:46.472 [INFO] {a0b7b39450abab17c89e7515de9b5fe4} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:25:59.709 [INFO] {08b06e92deaeab177d4dc01ba92e4bea} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:26:48.753 [INFO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:26:48.754 [INFO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:26:48.760 [INFO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 15:31:11.479 [INFO] {08b06e92deaeab177d4dc01ba92e4bea} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:31:57.672 [INFO] {08b06e92deaeab177d4dc01ba92e4bea} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:32:06.945 [INFO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:32:06.946 [INFO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:32:06.949 [INFO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 15:34:22.149 [INFO] {08b06e92deaeab177d4dc01ba92e4bea} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:34:57.394 [INFO] {409639e45cafab173541190e6029adb2} Load into cache...
SonaMesh Version:  , Build Time: 
2024-01-19 15:35:00.638 [INFO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:35:00.639 [INFO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:35:00.639 [INFO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
2024-01-19 15:37:33.708 [INFO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:18: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:37:33.709 [INFO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:314: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:37:33.710 [INFO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:260: Get tts object with vccID: 111111 routeAccessCode: 000
