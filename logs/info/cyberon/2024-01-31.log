2024-01-31 13:43:19.164 [INFO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-31 13:43:19.177 [INFO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这个是一个测试的tts 文字",
	"type": "wav",
	"user_id": "123456",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 14:31:50.897 [INFO] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:529: Recognize file: /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/audio_01.wav
2024-01-31 14:31:50.898 [INFO] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:72: Connect to host
2024-01-31 14:39:00.382 [INFO] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:529: Recognize file: /var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/audio_01.wav
2024-01-31 14:39:00.383 [INFO] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:72: Connect to host
2024-01-31 14:42:58.013 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:72: Connect to host
2024-01-31 14:42:58.257 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).Start] cyberon_stt.go:358: Cybreron_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:9wbozm01kyecysork2gc7vc100m4nttl VccID:111111 RouteAccessCode:000} 
2024-01-31 14:42:58.257 [INFO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:207: Start receive message... 
2024-01-31 16:39:03.377 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:47: Connect to host
2024-01-31 16:39:03.378 [INFO] {e8366a2ad361af176518674b9df69085} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试",
	"type": "wav",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-31 16:39:15.867 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:47: Connect to host
2024-01-31 16:39:15.868 [INFO] {f066e712d661af176618674b4fed8819} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "塞维tts测试文字",
	"type": "mp3",
	"user_id": "0987",
	"vccid": "111111",
	"volume": 1.1
}
