2024-01-19 11:39:15.725 [INFO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 11:39:15.725 [INFO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:41:21.834 [INFO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 11:41:21.835 [INFO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:42:36.191 [INFO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 11:42:36.191 [INFO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:02.862 [INFO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 11:43:02.863 [INFO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 11:43:41.633 [INFO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 11:43:41.635 [INFO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"type": "wav",
	"user_id": "1233",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 12:04:01.530 [INFO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 12:04:01.531 [INFO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个cyberon tts的测试",
	"type": "wav",
	"user_id": "111",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-19 13:49:00.957 [INFO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 13:49:00.958 [INFO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 13:49:25.352 [INFO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 13:49:25.356 [INFO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "tts測試",
	"type": "wav",
	"user_id": "1111",
	"vccid": "111111",
	"volume": 1.4
}
2024-01-19 15:26:48.762 [INFO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 15:26:48.766 [INFO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:91: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:32:06.949 [INFO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 15:32:06.953 [INFO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:35:00.639 [INFO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 15:35:51.593 [INFO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
2024-01-19 15:37:33.710 [INFO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-19 15:39:02.728 [INFO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": " 這是一個tts測試",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.5
}
