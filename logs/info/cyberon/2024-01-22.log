2024-01-22 10:48:31.196 [INFO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 10:48:31.200 [INFO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:51:41.221 [INFO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 10:51:41.222 [INFO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:54:32.114 [INFO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 10:54:47.061 [INFO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 10:55:30.022 [INFO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 10:58:05.244 [INFO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:05:51.810 [INFO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 11:05:51.812 [INFO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:06:57.552 [INFO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 11:06:57.553 [INFO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
2024-01-22 11:09:34.144 [INFO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/logic/cyberon.(*TTS).Connect] cyberon_tts.go:46: Connect to host
2024-01-22 11:09:41.300 [INFO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:90: Cyberon_TTS : {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "这是一个tts 测试",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.1
}
