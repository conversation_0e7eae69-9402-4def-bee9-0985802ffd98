2024-04-01 11:41:58.532 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).Connect] emotibot_stt.go:60: Connect to host
2024-04-01 11:41:58.654 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:118: Start receive message... 
2024-04-01 11:41:58.654 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).Start] emotibot_stt.go:286: EmotiBot_STT_Start: &model.SttStartReq{Action:"start", ShortCommand:false, EnablePartial:false, CallID:"1joxdy21vepd08h47mlxk14100kya5xn", VccID:"111111", RouteAccessCode:"000"} 
2024-04-01 11:42:45.445 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).Stop] emotibot_stt.go:360: EmotiBot_STT_Stop: &{stop}
2024-04-01 11:42:45.446 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage.func1] emotibot_stt.go:120: Leave receive message... 
