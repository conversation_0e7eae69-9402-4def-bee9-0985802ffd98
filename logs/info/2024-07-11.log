STMesh Version:  , Build Time: 
2024-07-11 13:29:47.122 [INFO] {501591088311e117906b6456988dc843} Load into cache...
2024-07-11 13:30:52.484 [INFO] {f04bc8409211e117390c2d6c7f53c2d2} Load into cache...
STMesh Version:  , Build Time: 
2024-07-11 13:31:57.537 [INFO] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 3
}
2024-07-11 13:31:57.538 [INFO] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 6,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 3
}
2024-07-11 13:31:57.539 [INFO] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 13:32:12.357 [INFO] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 3
}
2024-07-11 13:32:12.358 [INFO] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 3
}
2024-07-11 13:32:25.393 [INFO] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:25.393 [INFO] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:36.128 [INFO] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. ",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:36.128 [INFO] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. ",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:36.128 [INFO] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 13:32:52.967 [INFO] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. test",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:52.967 [INFO] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. test",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:32:52.967 [INFO] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
STMesh Version:  , Build Time: 
2024-07-11 13:46:45.599 [INFO] {80f3cc2a7012e117343b9513ea1f2bd2} Load into cache...
2024-07-11 13:47:01.708 [INFO] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:47:01.709 [INFO] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:47:01.710 [INFO] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 13:47:30.868 [INFO] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc def",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:47:30.868 [INFO] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc def",
	"type": "wav",
	"user_id": "fafadf",
	"vccid": "111111",
	"volume": 40.6
}
2024-07-11 13:47:30.869 [INFO] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 14:31:43.844 [INFO] {20a6bd66e414e117ca0ccf5562764762} Load into cache...
STMesh Version:  , Build Time: 
2024-07-11 14:32:24.018 [INFO] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:32:24.021 [INFO] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:32:24.022 [INFO] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 14:33:23.321 [INFO] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:33:23.322 [INFO] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:33:23.322 [INFO] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 14:34:41.188 [INFO] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:34:41.190 [INFO] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:34:49.504 [INFO] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 14:34:49.504 [INFO] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 70.1
}
2024-07-11 16:59:15.305 [INFO] {d8c5f14af11ce1171872d76afc85ebf6} Load into cache...
STMesh Version:  , Build Time: 
2024-07-11 17:00:50.434 [INFO] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:00:50.434 [INFO] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:00:50.436 [INFO] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 17:01:15.656 [INFO] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:01:15.657 [INFO] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:01:15.658 [INFO] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 
STMesh Version:  , Build Time: 
2024-07-11 17:07:46.422 [INFO] {782de54b681de1173cea5c7e57d82002} Load into cache...
2024-07-11 17:07:57.270 [INFO] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:07:57.271 [INFO] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:07:57.273 [INFO] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 
2024-07-11 17:08:23.042 [INFO] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:08:23.044 [INFO] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:08:23.056 [INFO] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-07-11 17:11:22.641 [INFO] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:11:22.642 [INFO] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:11:22.643 [INFO] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 
2024-07-11 17:11:59.472 [INFO] {b8c5be36a31de1172348c62d59d46356} Load into cache...
STMesh Version:  , Build Time: 
2024-07-11 17:12:02.729 [INFO] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:44: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:12:02.729 [INFO] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:324: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "",
	"speed": 1,
	"text": "Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。\n个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。\nAzure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。\n定制声音 API 可用于创建和管理专业和个人神经网络定制声音。",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 61.7
}
2024-07-11 17:12:02.730 [INFO] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 
