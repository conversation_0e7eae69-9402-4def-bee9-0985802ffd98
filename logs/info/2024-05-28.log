2024-05-28 15:34:07.958 [INFO] {70c658acbf96d317e677e57801ac01be} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:35:05.849 [INFO] {70c658acbf96d317e677e57801ac01be} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:36:41.942 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:53286 connected ...
2024-05-28 15:36:41.942 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:126: msgType:1 msg:{"action":"start","call_id":"18unroewn20d1l3szce1o5s100rn83y5","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-05-28 15:36:41.942 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "18unroewn20d1l3szce1o5s100rn83y5",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-05-28 15:36:41.942 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:36:41.953 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:36:41.966 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:42.165 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:42.368 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:42.567 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:42.769 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:42.970 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:43.173 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:43.373 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:43.576 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:43.777 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:43.978 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:44.180 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:44.383 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:44.582 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:44.784 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:44.988 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:45.186 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:45.387 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:45.591 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:45.792 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:45.993 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:46.194 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:46.395 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:46.597 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:46.798 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:46.928 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:36:46.999 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:47.200 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:47.402 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:47.603 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:47.805 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:48.005 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:48.207 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:48.407 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:48.608 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:48.810 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:49.011 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:49.213 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:49.413 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:49.615 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:49.816 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:50.031 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:50.219 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:50.419 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:50.620 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:50.822 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:51.023 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:51.223 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:51.424 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:51.625 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:51.826 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:52.027 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:52.229 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:52.431 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:52.632 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:52.833 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:53.034 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:53.234 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:53.435 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:53.636 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:53.837 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:54.038 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:54.237 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:54.437 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:54.637 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:54.808 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。"
	},
	"status": 0
}
2024-05-28 15:36:54.837 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:55.038 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:55.238 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:55.438 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:55.640 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:55.840 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:56.040 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:56.241 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:56.441 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:56.569 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那。"
	},
	"status": 0
}
2024-05-28 15:36:56.641 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:56.841 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:57.042 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:57.243 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:57.444 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:57.645 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:57.846 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:58.046 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:58.248 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:58.449 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:58.651 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:58.853 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:59.054 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:59.256 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:59.456 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:59.657 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:36:59.858 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:00.059 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:00.260 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:00.461 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:00.662 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:00.863 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:01.064 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:01.265 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:01.466 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:01.668 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:01.868 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:02.070 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:02.271 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:02.472 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:02.672 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:02.874 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:03.074 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:03.275 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:03.477 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:03.677 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:03.878 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.080 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.280 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.481 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.682 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.883 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:04.917 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你們那個日期啊是那麼小，我以為是到12月31號就是到12月30號啊。"
	},
	"status": 0
}
2024-05-28 15:37:05.085 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:05.286 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:05.487 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:05.688 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:05.889 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:06.091 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:06.292 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:06.493 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:06.694 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:06.895 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:07.097 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:07.298 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:07.499 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:07.700 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:07.901 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:08.102 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:08.303 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:08.505 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:08.706 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:08.906 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:09.107 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:09.308 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:09.510 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:09.711 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:09.873 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "對，因為我們往年每一年的勸他都是到12月30號。"
	},
	"status": 0
}
2024-05-28 15:37:09.912 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:10.116 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:10.317 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:10.518 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:10.719 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:10.920 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:11.122 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:11.323 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:11.524 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:11.725 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:11.978 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:12.180 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:12.381 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:12.582 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:12.783 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:12.984 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:13.185 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:13.387 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:13.588 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:13.789 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:13.946 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-05-28 15:37:13.989 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:14.190 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:14.390 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:14.590 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:14.790 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:14.990 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:15.190 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:15.391 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:15.591 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:15.791 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:15.992 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:16.192 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:16.392 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:16.592 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:16.793 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:16.993 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:17.194 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:17.394 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:17.595 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:17.796 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:17.997 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:18.198 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:18.398 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:18.599 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:18.799 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:18.999 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:19.201 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:19.402 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:19.602 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:19.803 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:19.961 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是差一天，為什麼你們門市又不肯讓我？"
	},
	"status": 0
}
2024-05-28 15:37:20.004 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:20.207 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:20.406 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:20.607 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:20.808 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:21.009 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:21.210 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:21.412 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:21.538 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "折抵啊。"
	},
	"status": 0
}
2024-05-28 15:37:21.613 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:21.814 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:22.015 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:22.216 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:22.417 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:22.618 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:22.819 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:23.020 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:23.221 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:23.422 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:23.623 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:23.824 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:24.025 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:24.226 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:24.427 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:24.628 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:24.829 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:25.030 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:25.232 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:25.433 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:25.634 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:25.835 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:26.036 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:26.237 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:26.319 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哎，買很多杯買十幾杯。"
	},
	"status": 0
}
2024-05-28 15:37:26.438 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:26.639 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:26.840 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:27.042 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:27.242 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:27.443 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:27.644 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:27.845 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:28.046 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:28.247 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:28.448 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:28.648 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:28.850 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:29.051 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:29.252 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:29.453 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:29.654 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:29.855 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:30.056 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:30.257 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:30.458 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:30.660 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:30.861 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:31.062 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:31.263 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:31.464 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:31.665 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:31.866 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:32.068 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:32.269 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:32.470 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:32.671 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:32.872 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.073 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.274 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.475 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.676 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.877 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:33.908 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，不好意思，因為這不是插插幾天，而是因為他就是過期門市，就沒辦法收卷了。"
	},
	"status": 0
}
2024-05-28 15:37:34.078 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:34.279 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:34.480 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:34.681 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:34.882 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:35.084 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:35.285 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:35.486 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:35.687 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:35.888 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:36.088 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:36.290 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:36.490 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:36.690 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:36.890 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:37.090 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:37.290 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:37.490 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:37.691 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:37.891 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:38.091 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:38.291 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:38.492 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:38.692 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:38.892 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:39.093 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:39.293 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:39.494 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:39.694 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:39.895 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:40.096 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:40.297 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:40.498 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:40.699 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:40.900 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:41.101 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:41.303 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:41.503 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:41.704 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:41.904 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:42.106 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:42.307 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:42.507 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:42.708 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:42.910 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:43.111 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:43.312 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:43.513 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:43.714 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:43.915 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:44.116 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:44.317 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:44.518 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:44.719 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:44.920 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:45.121 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:45.323 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:45.413 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那這裡有沒有什麼方式可以？這個不我們啦，因為這個很多張欸，我有很多張，哎，不是一張而已，哎。"
	},
	"status": 0
}
2024-05-28 15:37:45.524 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:45.725 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:45.925 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:46.126 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:46.327 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:46.528 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:46.729 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:46.930 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:47.130 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:47.331 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:47.533 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:47.734 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:47.935 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:48.136 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:48.337 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:48.538 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:48.739 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:48.940 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:49.141 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:49.342 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:49.543 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:49.744 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:49.945 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:50.147 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:50.348 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:50.548 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:50.749 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:50.950 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:51.151 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:51.352 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:51.553 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:51.754 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:51.955 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:52.157 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:52.277 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我了解，但是因為他是一整年度的兌換，所以這個券的部分他沒有辦法再補，惟。"
	},
	"status": 0
}
2024-05-28 15:37:52.358 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:52.559 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:52.760 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:52.961 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:53.162 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:53.363 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:53.565 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:53.766 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:53.967 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:54.168 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:54.369 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:54.570 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:54.772 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:54.974 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:55.174 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:55.375 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:55.576 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:55.777 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:55.978 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:56.179 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:56.380 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:37:56.582 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:38:23.000 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-05-28 15:38:23.380 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那有沒有什麼方式可以彌補我們的這種損失啊？因為那個很。"
	},
	"status": 0
}
2024-05-28 15:38:23.384 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:338: Interrupting ... 
2024-05-28 15:44:47.092 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:44:57.910 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:53746 connected ...
2024-05-28 15:44:57.910 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:127: msgType:1 msg:{"action":"start","call_id":"ql23ku0x5f0d1l3zb6sego8100swkp0w","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-05-28 15:44:57.910 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "ql23ku0x5f0d1l3zb6sego8100swkp0w",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-05-28 15:44:57.910 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:44:57.919 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:44:57.926 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:58.127 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:58.328 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:58.529 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:58.729 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:58.930 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:59.131 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:59.333 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:59.534 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:59.736 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:44:59.937 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:00.139 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:00.339 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:00.540 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:00.742 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:00.942 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:01.144 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:01.346 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:01.546 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:01.746 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:01.946 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:02.148 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:02.350 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:02.551 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:02.752 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:02.891 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:45:02.953 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:03.155 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:03.355 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:03.555 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:03.756 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:03.957 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:04.159 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:04.360 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:04.562 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:04.763 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:04.966 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:05.166 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:05.367 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:05.569 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:05.771 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:05.971 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:06.172 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:06.374 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:06.575 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:06.775 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:06.977 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:07.178 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:07.379 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:07.581 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:07.781 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:07.982 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:08.185 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:08.386 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:08.588 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:08.789 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:08.990 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:09.196 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:09.398 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:09.599 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:09.802 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:10.003 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:10.204 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:10.406 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:10.607 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:10.776 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。"
	},
	"status": 0
}
2024-05-28 15:45:10.807 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:11.007 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:11.207 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:11.408 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:11.608 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:11.809 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:12.014 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:12.210 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:12.411 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:12.545 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那。"
	},
	"status": 0
}
2024-05-28 15:45:12.612 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:12.813 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:13.014 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:13.216 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:13.417 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:13.619 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:13.820 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:14.021 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:14.223 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:14.425 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:14.626 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:45:42.891 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-05-28 15:45:43.103 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你們那個日期啊。"
	},
	"status": 0
}
2024-05-28 15:45:43.106 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:338: Interrupting ... 
2024-05-28 15:46:45.606 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:53814 connected ...
2024-05-28 15:46:45.607 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:127: msgType:1 msg:{"action":"start","call_id":"ql23ku0y7c0d1l40onwbj0w1002kiids","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-05-28 15:46:45.607 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "ql23ku0y7c0d1l40onwbj0w1002kiids",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-05-28 15:46:45.608 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:46:45.608 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:46:45.615 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:45.816 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:46.017 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:46.218 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:46.419 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:46.621 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:46.822 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:47.023 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:47.224 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:47.425 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:47.626 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:47.831 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:48.032 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:48.233 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:48.434 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:48.636 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:48.837 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:49.039 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:49.240 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:49.441 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:49.643 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:49.844 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:50.048 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:50.248 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:50.450 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:50.590 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:46:50.651 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:50.852 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:51.053 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:51.255 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:51.458 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:51.659 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:51.860 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:52.061 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:52.263 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:52.465 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:52.664 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:52.865 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:53.066 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:53.268 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:53.472 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:53.671 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:53.871 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:54.073 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:54.274 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:54.475 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:54.676 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:54.878 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:55.080 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:55.281 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:55.482 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:55.682 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:55.884 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:56.086 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:56.287 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:56.489 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:56.690 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:56.893 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:57.093 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:57.295 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:57.497 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:57.698 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:57.899 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:58.100 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:58.301 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:58.477 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。"
	},
	"status": 0
}
2024-05-28 15:46:58.503 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:58.704 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:58.905 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:59.105 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:59.307 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:59.508 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:59.709 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:46:59.910 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:00.110 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:00.234 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那。"
	},
	"status": 0
}
2024-05-28 15:47:00.310 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:00.512 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:00.712 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:00.913 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:01.115 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:01.317 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:01.518 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:01.719 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:01.920 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:02.122 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:02.323 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:02.524 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:02.725 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:02.926 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:03.129 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:03.329 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:03.530 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:03.731 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:03.932 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:04.133 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:04.334 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:04.535 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:04.737 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:04.938 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:05.139 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:05.341 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:05.542 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:05.743 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:05.944 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:06.146 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:06.347 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:06.548 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:06.749 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:06.951 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:07.152 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:07.353 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:07.554 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:07.755 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:07.956 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:08.157 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:08.358 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:08.559 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:08.596 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你們那個日期啊是那麼小，我以為是到12月31號就是到12月30號啊。"
	},
	"status": 0
}
2024-05-28 15:47:08.761 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:08.962 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:09.163 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:09.364 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:09.566 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:09.767 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:09.968 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:10.169 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:10.371 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:10.572 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:10.773 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:10.975 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:11.175 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:11.377 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:11.579 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:11.779 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:11.981 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:12.181 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:12.383 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:12.584 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:12.785 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:12.986 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:13.188 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:13.388 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:13.545 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "對，因為我們往年每一年的勸他都是到12月30號。"
	},
	"status": 0
}
2024-05-28 15:47:13.589 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:13.790 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:13.991 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:14.192 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:14.394 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:14.595 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:14.796 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:14.997 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:15.198 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:15.400 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:15.600 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:15.802 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:16.003 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:16.203 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:16.403 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:16.603 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:16.804 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:17.004 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:17.204 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:47:43.591 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-05-28 15:47:43.756 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-05-28 15:47:43.758 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:338: Interrupting ... 
2024-05-28 15:48:18.566 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:53894 connected ...
2024-05-28 15:48:18.567 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:127: msgType:1 msg:{"action":"start","call_id":"ql23ku0ynv0d1l41vdaav00100jjz29s","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-05-28 15:48:18.567 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "ql23ku0ynv0d1l41vdaav00100jjz29s",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-05-28 15:48:18.567 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:48:18.568 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:48:18.575 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:18.776 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:18.977 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:19.178 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:19.379 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:19.580 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:19.781 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:19.982 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:20.183 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:20.384 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:20.584 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:20.785 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:20.986 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:21.187 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:21.388 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:21.589 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:21.790 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:21.990 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:22.191 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:22.392 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:22.593 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:22.794 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:22.995 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:23.196 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:23.397 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:23.530 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:48:23.599 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:23.646 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:48:23.800 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:24.001 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:24.202 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:24.403 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:24.604 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:24.805 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:25.008 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:25.208 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:25.409 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:25.610 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:25.811 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:26.012 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:26.213 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:26.414 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:26.615 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:26.817 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:27.018 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:27.219 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:27.420 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:27.621 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:27.822 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:28.023 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:28.225 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:28.426 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:28.627 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:28.828 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:29.029 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:29.230 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:29.431 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:29.632 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:29.834 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:30.035 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:30.236 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:30.437 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:30.638 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:30.840 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:31.040 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:31.241 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:31.442 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:31.644 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:31.845 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:32.046 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:32.247 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:32.448 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:32.649 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:32.851 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:33.052 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:33.253 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:33.454 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:33.655 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:33.856 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:34.057 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:34.258 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:34.459 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:34.660 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:34.861 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:35.063 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:35.263 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:35.463 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:35.663 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:35.863 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:36.064 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:36.264 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:36.464 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:36.665 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:36.865 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:37.065 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:37.266 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:37.466 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:37.667 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:37.867 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:38.068 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:38.268 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:38.469 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:38.669 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:38.870 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:39.071 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:39.271 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:39.472 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:39.673 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:39.874 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:40.075 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:40.276 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:40.477 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:40.678 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:40.879 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:41.080 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:41.281 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:41.483 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:41.684 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:41.884 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:42.085 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:42.286 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:42.487 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:42.688 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:42.889 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:43.090 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:43.291 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:43.492 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:43.693 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:43.894 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:44.095 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:44.296 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:44.497 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:44.698 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:44.900 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:45.101 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:45.302 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:45.503 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:45.704 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:45.905 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:46.107 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:46.307 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:46.508 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:46.710 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:46.911 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:47.112 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:47.313 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:47.514 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:47.715 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:47.917 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:48.118 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:48.319 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:48.519 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:48.720 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:48.920 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:49.122 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:49.323 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:49.523 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:49.724 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:49.925 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:50.126 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:50.327 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:50.529 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:50.730 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:50.931 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:51.132 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:51.333 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:51.534 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:51.735 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:51.937 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:52.138 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:52.339 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:52.540 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:52.741 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:52.941 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:53.143 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:53.344 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:53.545 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:53.746 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:53.947 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:54.148 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:54.349 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:54.550 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:54.752 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:54.953 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:55.154 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:55.355 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:55.556 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:55.757 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:55.958 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:56.160 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:56.361 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:56.562 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:56.763 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:56.963 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:57.164 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:57.364 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:57.564 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:57.764 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:57.964 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:58.165 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:58.365 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:58.565 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:58.766 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:58.966 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:59.167 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:59.367 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:59.568 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:59.769 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:48:59.970 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:00.170 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:00.372 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:00.572 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:00.773 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:00.974 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:01.175 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:01.376 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:01.577 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:01.779 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:01.980 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:02.181 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:02.381 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:02.583 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:02.784 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:02.985 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:03.186 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:03.387 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:03.588 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:03.789 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:03.990 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:04.191 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:04.391 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:04.592 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:04.793 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:04.994 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:05.195 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:05.396 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:05.596 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:05.797 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:05.999 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:06.199 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:06.400 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:06.602 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:06.803 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:07.004 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:07.205 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:07.406 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:07.607 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:07.808 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:08.009 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:08.210 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:08.412 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:08.613 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:08.814 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:09.014 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:09.215 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:09.417 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:09.617 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:09.818 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:10.019 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:10.220 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:10.421 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:10.622 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:10.823 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:11.024 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:11.226 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:11.426 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:11.627 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:11.828 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:12.029 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:12.230 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:12.431 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:12.633 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:12.834 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:13.035 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:13.236 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:13.437 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:13.638 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:13.839 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:14.040 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:14.241 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:14.442 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:14.643 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:14.844 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:15.045 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:15.246 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:15.448 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:15.649 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:15.850 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:16.051 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:16.252 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:16.453 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:16.654 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:16.855 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:17.056 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:17.257 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:17.458 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:17.659 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:17.859 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:18.060 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:18.261 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:18.463 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:18.664 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:18.863 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:19.064 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:19.264 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:19.464 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:19.664 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:19.864 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:49:20.064 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
STMesh Version:  , Build Time: 
2024-05-28 15:50:41.455 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
2024-05-28 15:50:53.010 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:54048 connected ...
2024-05-28 15:50:53.011 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:131: msgType:1 msg:{"action":"start","call_id":"ql23ku0zpg0d1l43ubi7un41005u2h15","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-05-28 15:50:53.011 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "ql23ku0zpg0d1l43ubi7un41005u2h15",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-05-28 15:50:53.011 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:50:53.013 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:50:53.020 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:53.222 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:53.422 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:53.623 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:53.824 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:54.025 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:54.226 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:54.427 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:54.628 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:54.829 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:55.030 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:55.231 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:55.432 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:55.634 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:55.835 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:56.035 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:56.236 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:56.437 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:56.638 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:56.839 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.040 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.242 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.443 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.644 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.845 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:57.978 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:50:58.045 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:58.246 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:58.448 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:58.648 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:58.850 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:59.051 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:59.251 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:59.451 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:59.652 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:50:59.853 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:00.054 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:00.256 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:00.457 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:00.658 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:00.859 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:01.060 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:01.261 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:01.462 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:01.663 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:01.864 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:02.066 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:02.267 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:02.471 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:02.671 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:02.872 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:03.072 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:03.273 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:03.474 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:03.675 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:03.876 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:04.077 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:04.278 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:04.479 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:04.681 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:04.882 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:05.083 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:05.284 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:05.485 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:05.686 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:05.858 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。"
	},
	"status": 0
}
2024-05-28 15:51:05.887 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:06.088 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:06.289 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:06.490 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:06.691 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:06.892 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:07.093 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:07.294 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:51:35.951 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-05-28 15:51:36.086 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那。"
	},
	"status": 0
}
2024-05-28 15:51:36.086 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:338: Interrupting ... 
2024-05-28 15:52:59.983 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:53:03.158 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:54136 connected ...
2024-05-28 15:53:03.158 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:135: msgType:1 msg:{"action":"start","call_id":"ql23ku0zwz0d1l45i3wl0w0100bmz231","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-05-28 15:53:03.158 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "ql23ku0zwz0d1l45i3wl0w0100bmz231",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-05-28 15:53:03.158 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-05-28 15:53:03.160 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-05-28 15:53:03.167 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:03.369 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:03.570 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:03.771 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:03.971 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:04.172 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:04.373 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:04.574 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:04.775 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:04.976 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:05.177 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:05.378 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:05.579 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:05.780 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:05.981 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:06.182 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:06.383 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:06.584 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:06.785 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:06.987 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:07.188 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:07.389 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:07.590 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:07.791 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:07.992 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:08.127 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你好。"
	},
	"status": 0
}
2024-05-28 15:53:08.193 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:08.394 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:08.595 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:08.796 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:08.998 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:09.199 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:09.400 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:09.601 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:09.802 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:10.003 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:10.203 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:10.404 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:10.605 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:10.806 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:11.007 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:11.208 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:11.409 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:11.610 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:11.811 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:12.012 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:12.213 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:12.414 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:12.615 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:12.816 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:13.017 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:13.218 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:13.419 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:13.620 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:13.821 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:14.022 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:14.223 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:14.424 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:14.626 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:14.827 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.028 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.229 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.430 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.631 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.832 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:15.987 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。"
	},
	"status": 0
}
2024-05-28 15:53:16.033 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:16.234 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:16.434 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:16.636 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:16.837 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:17.037 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:17.238 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:17.439 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:17.640 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:17.764 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那。"
	},
	"status": 0
}
2024-05-28 15:53:17.841 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:18.042 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:18.243 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:18.444 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-05-28 15:53:48.080 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-05-28 15:53:48.330 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "你們那個。"
	},
	"status": 0
}
2024-05-28 15:53:48.331 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/stt.(*sSTT).Interrupt] stt.go:338: Interrupting ... 
2024-05-28 15:56:47.594 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:57:57.397 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 15:59:42.462 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:00:04.468 [INFO] {5880f7922d98d317469036150d12326c} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:00:04.827 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:00:47.611 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:03:36.935 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:14:52.303 [INFO] {70c62ae85697d317aa2e4d1dd43c9764} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:16:28.411 [INFO] {e8d884aa1299d317b350d0788de9e5a7} Load into cache...
STMesh Version:  , Build Time: 
STMesh Version:  , Build Time: 
2024-05-28 16:18:52.799 [INFO] {088ac7343399d317b9e1186570ed07fa} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:22:50.584 [INFO] {088ac7343399d317b9e1186570ed07fa} Load into cache...
2024-05-28 16:24:45.654 [INFO] {088ac7343399d317b9e1186570ed07fa} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 16:28:42.320 [INFO] {088ac7343399d317b9e1186570ed07fa} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 17:14:42.478 [INFO] {e8484e31409cd3173fb3fc0ab8373ded} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58050 connected ...
2024-05-28 17:14:47.474 [INFO] {58251d5b419cd31740b3fc0a9e728a99} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58051 connected ...
2024-05-28 17:14:52.473 [INFO] {f8a92285429cd31741b3fc0abd5ccd7f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58066 connected ...
2024-05-28 17:14:57.474 [INFO] {30512caf439cd31742b3fc0af60ca987} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58067 connected ...
2024-05-28 17:15:02.471 [INFO] {001112d9449cd31743b3fc0ac2f80d5d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58079 connected ...
2024-05-28 17:16:05.313 [INFO] {a86c0a42539cd317e715f73699562816} Load into cache...
STMesh Version:  , Build Time: 
2024-05-28 17:16:13.776 [INFO] {c0732673559cd317c9d2d976a8b6e2c4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58175 connected ...
2024-05-28 17:16:18.771 [INFO] {28a0da9c569cd317cad2d976301af5f2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58176 connected ...
2024-05-28 17:16:23.770 [INFO] {584fd5c6579cd317cbd2d976b3614bb3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58191 connected ...
2024-05-28 17:16:28.771 [INFO] {20afe6f0589cd317ccd2d976b6774eed} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:40: Client [::1]:58192 connected ...
