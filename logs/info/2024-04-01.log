STMesh Version:  , Build Time: 
2024-04-01 11:39:53.992 [INFO] {60caffc0e40ac217bcdab002e4fac3a1} Load into cache...
2024-04-01 11:39:59.243 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:56260 connected ...
2024-04-01 11:39:59.243 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"1joxdy21vapd08h2otsboy01001y0ked","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-04-01 11:39:59.243 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "1joxdy21vapd08h2otsboy01001y0ked",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-04-01 11:39:59.244 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-04-01 11:39:59.426 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-04-01 11:39:59.435 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:39:59.636 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:39:59.836 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:00.036 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:00.239 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:00.439 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:00.638 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:00.839 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:01.040 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:01.240 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:01.441 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:01.642 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:01.844 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:02.045 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:02.245 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:02.446 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:02.649 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:02.849 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:03.050 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:03.250 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:03.451 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:03.653 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:03.854 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:04.055 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:04.256 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:04.457 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:04.657 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:04.679 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "大哥你好"
	},
	"status": 0
}
2024-04-01 11:40:04.858 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:05.060 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:05.267 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:05.471 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:05.669 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:05.873 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:06.076 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:06.273 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:06.472 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:06.675 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:06.874 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:07.076 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:07.276 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:07.478 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:07.678 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:07.881 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:08.092 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:08.280 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:08.492 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:08.692 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:08.892 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:09.093 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:09.293 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:09.494 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:09.696 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:09.898 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:10.098 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:10.300 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:10.500 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:10.702 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:10.903 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:11.106 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:11.308 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:11.510 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:11.710 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:11.923 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:12.123 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:12.325 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:12.525 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:12.729 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:12.930 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:13.130 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:13.332 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:13.533 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:13.740 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:13.934 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:14.141 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:14.340 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:14.540 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:14.664 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐我想要反應一下我買那個整本的那個筆記本會不會有咖啡券嗎是"
	},
	"status": 0
}
2024-04-01 11:40:14.741 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:14.942 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:15.143 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:15.343 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:15.545 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:15.746 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:15.947 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:16.148 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:16.350 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:16.552 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:16.752 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:16.952 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:17.153 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:17.354 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:17.555 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:17.700 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊是這麼"
	},
	"status": 0
}
2024-04-01 11:40:17.755 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:17.874 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小我以為"
	},
	"status": 0
}
2024-04-01 11:40:17.957 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:18.160 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:18.360 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:18.561 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:18.762 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:18.963 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:19.163 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:19.365 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:19.565 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:19.766 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:19.965 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:20.171 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:20.368 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:20.569 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:20.770 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:20.970 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:21.172 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:21.373 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:21.574 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:21.775 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:21.976 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:22.177 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:22.378 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:22.579 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:22.780 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:22.982 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:23.073 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "知道十二月三十一號就是到十二月三十號"
	},
	"status": 0
}
2024-04-01 11:40:23.182 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:23.383 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:23.584 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:23.787 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:23.988 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:24.162 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "對因為我們往年每一年的"
	},
	"status": 0
}
2024-04-01 11:40:24.189 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:24.392 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:24.591 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:24.793 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:24.994 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:25.195 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:25.396 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:25.598 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:25.799 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:26.000 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:26.205 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:26.407 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:26.608 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:26.808 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.009 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.210 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.411 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.612 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.814 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:27.920 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "券他都是到十二月三十號"
	},
	"status": 0
}
2024-04-01 11:40:28.015 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:28.217 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:28.417 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:28.619 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:28.822 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:29.022 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:29.224 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:29.428 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:29.626 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:29.828 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:30.029 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:30.229 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:30.435 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:30.635 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:30.836 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:31.040 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:31.241 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:31.442 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:31.643 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:31.844 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:32.044 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:32.244 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:32.446 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:32.647 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:32.770 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "所以券的使用期限確實是到十二月三十"
	},
	"status": 0
}
2024-04-01 11:40:32.847 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:33.048 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:33.249 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:33.451 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:33.654 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:33.853 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:34.053 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:34.255 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:34.455 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:34.657 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:34.858 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:35.059 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:35.260 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:35.461 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:35.663 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:35.866 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:36.066 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:36.269 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:36.472 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:36.672 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:36.875 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:37.076 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:37.276 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:37.483 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:37.682 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:37.883 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:38.085 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:38.285 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:38.486 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:38.691 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:38.891 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:39.092 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:39.250 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是他一天為什麼你們門市又不肯讓我"
	},
	"status": 0
}
2024-04-01 11:40:39.293 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:39.494 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:39.523 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "則底啊"
	},
	"status": 0
}
2024-04-01 11:40:39.695 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:39.896 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:40.098 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:40.297 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:40.498 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:40.699 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:40.900 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:41.100 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:41.302 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:41.503 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:41.703 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:41.904 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:42.105 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:42.306 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:42.507 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:42.710 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:42.869 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐我這邊還買了很多杯買"
	},
	"status": 0
}
2024-04-01 11:40:42.910 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:43.112 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:43.314 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:43.514 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:43.715 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:43.916 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:44.019 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "十幾杯"
	},
	"status": 0
}
2024-04-01 11:40:44.117 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:44.323 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:44.531 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:44.731 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:44.932 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:45.132 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:45.334 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:45.537 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:45.737 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:45.938 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:46.138 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:46.338 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:46.539 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:46.739 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:46.939 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:47.140 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:47.340 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:47.540 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:47.741 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:47.942 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:48.143 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:48.344 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:48.544 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:48.745 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:48.946 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:49.147 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:49.348 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:49.548 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:49.749 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:49.950 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:50.150 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:50.351 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:50.552 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:50.753 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:50.955 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:51.156 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:51.361 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:51.561 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:51.762 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:51.963 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:52.163 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:52.364 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:52.565 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:52.766 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:52.968 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:53.169 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:53.370 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:53.570 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:53.644 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐不好意思因為這不是差差幾千而是因為他就是過期門市就沒辦法收卷了"
	},
	"status": 0
}
2024-04-01 11:40:53.772 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:53.973 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:54.174 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:54.378 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:54.588 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:54.781 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:54.982 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:55.186 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:55.388 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:55.589 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:55.790 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:55.991 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:56.192 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:56.392 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:56.593 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:56.794 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:56.997 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:57.198 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:57.399 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:57.601 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:57.802 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:58.003 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:58.205 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:58.406 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:58.607 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:58.808 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:59.009 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:59.210 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:59.410 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:59.611 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:40:59.812 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:00.013 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:00.214 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:00.416 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:00.618 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:00.821 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:01.020 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:01.221 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:01.421 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:01.623 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:01.823 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:02.024 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:02.225 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:02.426 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:02.627 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:02.829 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:03.030 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:03.231 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:03.432 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:03.634 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:03.835 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:04.039 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:04.239 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:04.439 [INFO] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:55.351 [INFO] {703d21b3020bc217a7426167642103ab} Load into cache...
STMesh Version:  , Build Time: 
2024-04-01 11:41:58.532 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:56386 connected ...
2024-04-01 11:41:58.532 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"1joxdy21vepd08h47mlxk14100kya5xn","enable_partial":false,"route_access_code":"000","short_command":false,"vccid":"111111"}

2024-04-01 11:41:58.532 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "1joxdy21vepd08h47mlxk14100kya5xn",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": false,
	"vccid": "111111"
}
2024-04-01 11:41:58.532 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-04-01 11:41:58.725 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-04-01 11:41:58.732 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:58.932 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:59.134 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:59.333 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:59.534 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:59.734 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:41:59.935 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:00.138 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:00.337 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:00.538 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:00.739 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:00.940 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:01.141 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:01.341 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:01.543 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:01.744 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:01.944 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:02.149 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:02.349 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:02.550 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:02.750 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:02.951 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:03.152 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:03.353 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:03.554 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:03.556 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -0.81,
		"transcript": "麻煩你好"
	},
	"status": 0
}
2024-04-01 11:42:03.755 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:03.959 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:04.161 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:04.363 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:04.563 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:04.765 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:04.973 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:05.174 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:05.374 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:05.581 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:05.777 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:05.977 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:06.177 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:06.383 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:06.584 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:06.787 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:06.988 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:07.189 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:07.389 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:07.591 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:07.791 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:07.991 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:08.192 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:08.394 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:08.596 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:08.796 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:08.998 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:09.199 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:09.401 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:09.602 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:09.804 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:10.004 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:10.206 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:10.407 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:10.610 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:10.811 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:11.013 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:11.212 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:11.413 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:11.559 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -9.51,
		"transcript": "小姐我想要反應一下我買那個整本的那個筆記本後面有咖啡券嘛是"
	},
	"status": 0
}
2024-04-01 11:42:11.614 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:11.816 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:12.018 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:12.219 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:12.420 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:12.621 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:12.824 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:13.029 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:13.228 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:13.229 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -0.19,
		"transcript": "那"
	},
	"status": 0
}
2024-04-01 11:42:13.428 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:13.629 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:13.829 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:14.030 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:14.233 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:14.437 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:14.638 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:14.838 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:15.039 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:15.243 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:15.442 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:15.643 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:15.843 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:16.044 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:16.245 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:16.458 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:16.657 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:16.857 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:17.058 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:17.259 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:17.460 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:17.660 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:17.861 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:18.063 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:18.263 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:18.464 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:18.665 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:18.866 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:19.066 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:19.268 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:19.468 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:19.670 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:19.870 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:20.071 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:20.277 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:20.479 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:20.680 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:20.881 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:21.082 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:21.283 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:21.485 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:21.674 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -7.97,
		"transcript": "你們那個日期啊是這麼小我以為是到十二月三十一號結果是到十二月三十號耶"
	},
	"status": 0
}
2024-04-01 11:42:21.685 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:21.886 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:22.091 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:22.290 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:22.491 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:22.692 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:22.894 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:23.094 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:23.297 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:23.497 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:23.698 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:23.899 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:24.100 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:24.301 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:24.503 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:24.703 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:24.904 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:25.105 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:25.306 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:25.509 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:25.710 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:25.912 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:26.112 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:26.313 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:26.515 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:26.616 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -2.42,
		"transcript": "對因為我們往年每一年的券他都是到十二月三十號"
	},
	"status": 0
}
2024-04-01 11:42:26.714 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:26.916 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:27.117 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:27.318 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:27.519 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:27.720 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:27.922 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:28.128 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:28.330 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:28.530 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:28.730 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:28.930 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:29.131 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:29.331 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:29.531 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:29.732 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:29.933 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:30.133 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:30.334 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:30.536 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:30.737 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:30.938 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:31.042 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": -1.68,
		"transcript": "所以券的使用期限確實是到十二月三十"
	},
	"status": 0
}
2024-04-01 11:42:31.138 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:31.339 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:31.541 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:31.743 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:31.943 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:32.144 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:32.344 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:32.545 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:32.746 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:32.948 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:33.148 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:33.357 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:33.561 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:33.759 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:33.963 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:34.160 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:34.361 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:34.562 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:34.762 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:34.966 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:35.164 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:35.370 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-04-01 11:42:45.442 [INFO] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": 102
}
