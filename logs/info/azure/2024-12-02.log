2024-12-02 15:14:58.113 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:14:58.113 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:14:58.113 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:14:58.114 [INFO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvq2z0d6112rfrzffs2007qs2he VccID:111111 RouteAccessCode:000}
2024-12-02 15:14:58.114 [INFO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvq2z0d6112rfryt28100wcnm8z VccID:111111 RouteAccessCode:000}
2024-12-02 15:14:58.114 [INFO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvq2z0d6112rfrzg7k300a911bu VccID:111111 RouteAccessCode:000}
2024-12-02 15:18:15.734 [INFO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:20:39.779 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:20:39.785 [INFO] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqjk0d61174ebqet4100moe3h2 VccID:111111 RouteAccessCode:000}
2024-12-02 15:21:15.543 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:21:15.545 [INFO] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqmg0d6117ktt2j5s100qpopa0 VccID:111111 RouteAccessCode:000}
2024-12-02 15:24:07.701 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:24:07.706 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqto0d6119rwyro4w100au24dn VccID:111111 RouteAccessCode:000}
2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:24:31.686 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqv00d611a2xnbxpc100xk0ony VccID:111111 RouteAccessCode:000}
2024-12-02 15:24:49.641 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: {"action":"stop"}
2024-12-02 15:24:49.642 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: &{Action:stop}
2024-12-02 15:24:50.032 [INFO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:682: Azure_STT_Interrupt: ... 
2024-12-02 15:25:15.641 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: {"action":"stop"}
2024-12-02 15:25:15.643 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: &{Action:stop}
2024-12-02 15:25:15.851 [INFO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:682: Azure_STT_Interrupt: ... 
2024-12-02 15:25:33.489 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:25:33.489 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:25:33.489 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:25:33.490 [INFO] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqy90d611avbq4520200xlv4os VccID:111111 RouteAccessCode:000}
2024-12-02 15:25:33.490 [INFO] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqy90d611avbq45201006lgkmt VccID:111111 RouteAccessCode:000}
2024-12-02 15:25:33.490 [INFO] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvqy90d611avbq528g3006pri6c VccID:111111 RouteAccessCode:000}
2024-12-02 15:28:22.877 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:28:22.877 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:28:22.877 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:28:22.880 [INFO] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvr7z0d611d154bjkg100uc217k VccID:111111 RouteAccessCode:000}
2024-12-02 15:28:22.880 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvr7z0d611d154c4eg300h7vxll VccID:111111 RouteAccessCode:000}
2024-12-02 15:28:22.880 [INFO] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvr7z0d611d154c3mo20070hznm VccID:111111 RouteAccessCode:000}
2024-12-02 15:30:03.852 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: {"action":"stop"}
2024-12-02 15:30:03.853 [INFO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:484: Azure_STT_Stop: &{Action:stop}
2024-12-02 15:36:45.806 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:36:45.806 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:36:45.806 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:36:45.812 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvrta0d611jg6ncqe8300t9cot1 VccID:111111 RouteAccessCode:000}
2024-12-02 15:36:45.812 [INFO] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvrta0d611jg6nc2h4200p8xesg VccID:111111 RouteAccessCode:000}
2024-12-02 15:36:45.812 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvrta0d611jg6nb8ds100wpqsi4 VccID:111111 RouteAccessCode:000}
2024-12-02 15:38:26.772 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:487: Azure_STT_Stop: {"action":"stop"}
2024-12-02 15:38:26.772 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:487: Azure_STT_Stop: {"action":"stop"}
2024-12-02 15:38:26.775 [INFO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:487: Azure_STT_Stop: &{Action:stop}
2024-12-02 15:38:26.775 [INFO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:487: Azure_STT_Stop: &{Action:stop}
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:40:56.792 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:40:56.792 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:72: Connect to host
2024-12-02 15:40:56.798 [INFO] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvs470d611mnhi2po81006foo45 VccID:111111 RouteAccessCode:000}
2024-12-02 15:40:56.798 [INFO] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvs470d611mnhi3f4w200rfmz2o VccID:111111 RouteAccessCode:000}
2024-12-02 15:40:56.798 [INFO] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:146: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:1arspqvs470d611mnhi3rhc300cnf85c VccID:111111 RouteAccessCode:000}
