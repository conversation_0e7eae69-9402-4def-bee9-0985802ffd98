2024-06-01 18:55:47.169 [INFO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-06-01 18:55:47.169 [INFO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-06-01 18:55:47.169 [INFO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-06-01 18:55:47.172 [INFO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:18e7geik6x0d1omjlcq1p4w100o3aand VccID:111111 RouteAccessCode:000}
2024-06-01 18:55:47.172 [INFO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:18e7geik6x0d1omjlcq33ag200d44d2c VccID:111111 RouteAccessCode:000}
2024-06-01 18:55:47.172 [INFO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:18e7geik6x0d1omjlcq3b08300bac2hv VccID:111111 RouteAccessCode:000}
2024-06-01 18:56:46.107 [INFO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-06-01 18:56:46.108 [INFO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-06-01 19:00:33.166 [INFO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-06-01 19:00:33.168 [INFO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-06-01 19:00:33.455 [INFO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
2024-06-01 19:00:43.167 [INFO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-06-01 19:00:43.172 [INFO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-06-01 19:00:43.303 [INFO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
