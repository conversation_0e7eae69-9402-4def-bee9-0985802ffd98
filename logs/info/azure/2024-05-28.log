2024-05-28 15:36:41.943 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:36:41.948 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:18unroewn20d1l3szce1o5s100rn83y5 VccID:111111 RouteAccessCode:000}
2024-05-28 15:38:23.000 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-05-28 15:38:23.002 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-05-28 15:38:23.384 [INFO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
2024-05-28 15:44:57.910 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:44:57.914 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:ql23ku0x5f0d1l3zb6sego8100swkp0w VccID:111111 RouteAccessCode:000}
2024-05-28 15:45:42.891 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-05-28 15:45:42.892 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-05-28 15:45:43.107 [INFO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
2024-05-28 15:46:45.608 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:46:45.608 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:ql23ku0y7c0d1l40onwbj0w1002kiids VccID:111111 RouteAccessCode:000}
2024-05-28 15:47:43.591 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-05-28 15:47:43.595 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-05-28 15:47:43.758 [INFO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
2024-05-28 15:48:18.567 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:48:18.567 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:true EnablePartial:false CallID:ql23ku0ynv0d1l41vdaav00100jjz29s VccID:111111 RouteAccessCode:000}
2024-05-28 15:48:18.568 [INFO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:228: Wait recognition of short command ...
2024-05-28 15:50:53.011 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:50:53.012 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:ql23ku0zpg0d1l43ubi7un41005u2h15 VccID:111111 RouteAccessCode:000}
2024-05-28 15:51:35.951 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-05-28 15:51:35.952 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-05-28 15:51:36.086 [INFO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
2024-05-28 15:53:03.158 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:50: Connect to host
2024-05-28 15:53:03.159 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).Start] azure_stt.go:120: Azure_STT_Start: &{Action:start ShortCommand:false EnablePartial:false CallID:ql23ku0zwz0d1l45i3wl0w0100bmz231 VccID:111111 RouteAccessCode:000}
2024-05-28 15:53:48.080 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: {"action":"stop"}
2024-05-28 15:53:48.084 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:430: Azure_STT_Stop: &{Action:stop}
2024-05-28 15:53:48.331 [INFO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).Interrupt] azure_stt.go:605: Azure_STT_Interrupt: ... 
