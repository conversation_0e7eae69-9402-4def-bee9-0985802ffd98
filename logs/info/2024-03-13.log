2024-03-13 11:04:34.194 [INFO] {e8e9c8beee33bc173a077d27272420a0} Load into cache...
STMesh Version:  , Build Time: 
2024-03-13 11:04:56.379 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:60773 connected ...
2024-03-13 11:04:56.379 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zef0czsafi5m6rw01005xw10e","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:04:56.380 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zef0czsafi5m6rw01005xw10e",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:04:56.380 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:04:56.391 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:04:56.392 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.392 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.392 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.392 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.392 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.397 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.397 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.397 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.398 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.398 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.398 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.399 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.399 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.399 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.400 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.400 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.401 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.401 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.401 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.402 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.402 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.403 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.403 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.403 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.403 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.404 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.404 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.405 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.406 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.406 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.406 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.407 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.407 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.408 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.408 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.408 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.409 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.409 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.409 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.410 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.410 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.410 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.411 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.411 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.412 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.413 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.413 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.413 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.413 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.413 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.414 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.414 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.414 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.415 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.415 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.416 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.416 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.416 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.417 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.417 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.417 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.418 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.418 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.418 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.424 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.424 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.425 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.425 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.425 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.426 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.427 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.427 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.428 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.428 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.428 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.428 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.429 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.429 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.429 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.429 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.429 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.430 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.430 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.431 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.432 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.432 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.432 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.438 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.438 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.438 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.438 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.438 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.439 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.439 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.439 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.440 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.440 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.440 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.440 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.440 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.441 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.441 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.441 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.442 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.443 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.443 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.443 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.443 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.443 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.444 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.444 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.444 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.445 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.445 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.445 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.445 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.445 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.446 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.447 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.447 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.448 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.449 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.449 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.449 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.449 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.450 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.450 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.450 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.450 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.455 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.455 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.455 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.456 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.456 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.456 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.457 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.457 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.457 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.457 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.457 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.458 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.458 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.458 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.458 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.459 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.459 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.459 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.459 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.460 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.461 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.461 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.461 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.462 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.462 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.462 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 1024  ...
2024-03-13 11:04:56.462 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 22  ...
2024-03-13 11:04:59.588 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:05:30.378 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:05:30.379 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:05:30.379 [INFO] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:07:22.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:61197 connected ...
2024-03-13 11:07:22.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zht0czsahdgltjgo100m2y2nc","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:07:22.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zht0czsahdgltjgo100m2y2nc",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:07:22.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:07:22.889 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:07:22.892 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.892 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.892 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.892 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.893 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.893 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.893 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.894 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.894 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.894 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.894 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.894 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.895 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.895 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.895 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.896 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.896 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.896 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.896 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.896 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.897 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.897 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.897 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.897 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.898 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.898 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.898 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.898 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.898 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.899 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:07:22.899 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:07:25.846 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:07:55.887 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:07:55.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:07:55.888 [INFO] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:10:18.029 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:61716 connected ...
2024-03-13 11:10:18.029 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zkv0czsajlx45f3k100if6bze","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:10:18.029 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zkv0czsajlx45f3k100if6bze",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:10:18.029 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:10:18.030 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:10:18.032 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:18.533 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:19.034 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:19.536 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:20.037 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:20.538 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:21.039 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:21.542 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:22.044 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:22.544 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:23.044 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:23.545 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:24.048 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:24.549 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:25.049 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:25.550 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:26.051 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:26.553 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:27.054 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:27.556 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:28.062 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:28.560 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:29.064 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:29.562 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:30.063 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:30.563 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:31.065 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:31.566 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:32.068 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:32.569 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:10:32.877 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:10:33.069 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:11:03.038 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:11:03.040 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:11:03.041 [INFO] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:11:47.652 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62019 connected ...
2024-03-13 11:11:47.652 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zn30czsakr3brkts100wsy7c5","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:11:47.653 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zn30czsakr3brkts100wsy7c5",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:11:47.653 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:11:47.654 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:11:47.655 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:48.156 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:48.656 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:49.160 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:49.661 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:50.162 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:50.664 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:51.165 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:51.667 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:52.167 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:52.669 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:53.171 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:53.679 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:54.179 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:54.681 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:55.181 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:55.657 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:11:55.682 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:56.184 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:56.687 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:57.184 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:57.686 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:58.188 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:58.688 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:59.189 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:11:59.690 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:00.191 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:00.692 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:01.193 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:01.693 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:02.194 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:02.695 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:12:02.696 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:12:02.696 [INFO] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:12:30.268 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62159 connected ...
2024-03-13 11:12:30.268 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zo90czsalao3vry010031a76b","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:12:30.268 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zo90czsalao3vry010031a76b",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:12:30.269 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:12:30.270 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:12:30.271 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:30.772 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:31.273 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:31.777 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:32.276 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:32.779 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:33.279 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:33.785 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:34.283 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:34.785 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:35.286 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:35.787 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:36.287 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:36.792 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:37.294 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:37.794 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:38.296 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:38.797 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:39.299 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:39.799 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:40.271 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:12:40.300 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:40.801 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:41.302 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:41.803 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:42.304 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:42.805 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:43.308 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:43.811 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:44.311 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:44.812 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:45.313 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:12:45.313 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:12:45.314 [INFO] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:12:55.547 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62231 connected ...
2024-03-13 11:12:55.548 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zp50czsalma6s2wo100nt5pna","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:12:55.548 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zp50czsalma6s2wo100nt5pna",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:12:55.548 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:12:55.549 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:12:55.550 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:56.051 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:56.552 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:57.053 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:57.554 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:58.055 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:58.556 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:59.058 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:12:59.557 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:00.059 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:00.560 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:01.062 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:01.564 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:02.064 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:02.566 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:03.067 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:03.567 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:04.068 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:04.570 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:05.074 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:05.577 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:06.077 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:06.580 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:07.081 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:07.582 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:08.086 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:08.589 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:09.089 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:09.551 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:13:09.590 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:10.091 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:10.595 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:13:10.595 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:13:10.595 [INFO] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:13:26.385 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62317 connected ...
2024-03-13 11:13:26.386 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zq30czsam0g6wbvc100a4fysq","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:13:26.386 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zq30czsam0g6wbvc100a4fysq",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:13:26.386 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:13:26.387 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:13:26.389 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:26.889 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:27.393 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:27.892 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:28.393 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:28.894 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:29.395 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:29.897 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:30.398 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:30.899 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:31.399 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:31.901 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:32.403 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:32.905 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:33.407 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:33.913 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:34.414 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:34.916 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:35.416 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:35.919 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:36.419 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:36.921 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:37.429 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:37.925 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:38.426 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:38.927 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:39.428 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:39.930 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:40.431 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:40.933 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:13:41.233 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:13:41.433 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:14:11.386 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:14:11.386 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:14:11.387 [INFO] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:15:52.701 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62784 connected ...
2024-03-13 11:15:52.701 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zt10czsanvnzkttk100igtp0g","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:15:52.702 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zt10czsanvnzkttk100igtp0g",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:15:52.702 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:15:52.703 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:15:52.705 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:53.005 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:53.307 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:53.611 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:53.908 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:54.209 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:54.526 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:54.826 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:55.127 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:55.429 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:55.730 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:56.032 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:56.332 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:56.632 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:56.933 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:57.234 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:57.537 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:57.840 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:58.141 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:58.442 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:58.745 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:59.045 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:59.346 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:59.649 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:15:59.951 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:00.252 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:00.553 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:00.705 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:16:00.854 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:01.154 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:01.455 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:01.756 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:16:01.756 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:16:01.757 [INFO] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:16:18.280 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62884 connected ...
2024-03-13 11:16:18.280 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zud0czsao7f0inu8100wjoj2n","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:16:18.281 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zud0czsao7f0inu8100wjoj2n",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:16:18.281 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:16:18.282 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:16:18.283 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:18.584 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:18.886 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:19.187 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:19.488 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:19.790 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:20.093 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:20.394 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:20.695 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:20.996 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:21.298 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:21.598 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:21.898 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:22.198 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:22.499 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:22.801 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:23.101 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:23.402 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:23.703 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:24.004 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:24.305 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:24.606 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:24.906 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:25.207 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:25.508 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:25.810 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:26.115 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:26.414 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:26.714 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:27.014 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:16:27.294 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:16:27.315 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:16:58.275 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:16:58.275 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:16:58.276 [INFO] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:17:05.748 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:63038 connected ...
2024-03-13 11:17:05.749 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0zvi0czsaot820y6w100lke0xe","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:17:05.749 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0zvi0czsaot820y6w100lke0xe",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:17:05.749 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:17:05.750 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:17:05.752 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:05.953 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:06.153 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:06.358 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:06.555 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:06.756 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:06.956 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:07.157 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:07.358 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:07.559 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:07.760 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:07.960 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:08.161 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:08.362 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:08.563 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:08.764 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:08.964 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:09.166 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:09.365 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:09.567 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:09.767 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:09.968 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:10.168 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:10.369 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:10.570 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:10.772 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:10.972 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:11.173 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:11.375 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:11.576 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:17:11.778 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:17:12.079 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:17:42.748 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:17:42.749 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:17:42.751 [INFO] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:19:45.817 [INFO] {989b076ac534bc1779c7d05077f97e1b} Load into cache...
STMesh Version:  , Build Time: 
2024-03-13 11:19:48.546 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:63616 connected ...
2024-03-13 11:19:48.547 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"oidycg0104eczsaqw0fpfio100tae1ur","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 11:19:48.547 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:201: Speech to text - start: {
	"action": "start",
	"call_id": "oidycg0104eczsaqw0fpfio100tae1ur",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 11:19:48.547 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:62: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 11:19:48.554 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:19:48.556 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:48.757 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:48.958 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:49.159 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:49.360 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:49.561 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:49.762 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:49.963 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:50.164 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:50.364 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:50.565 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:50.766 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:50.968 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:51.169 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:51.369 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:51.571 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:51.773 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:51.973 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:52.176 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:52.378 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:52.579 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:52.780 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:52.981 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:53.182 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:53.384 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:53.585 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:53.786 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:53.987 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:54.188 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:54.388 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 6400  ...
2024-03-13 11:19:54.590 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:250: Send voice buffer ,buffer length 2582  ...
2024-03-13 11:19:54.674 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:168: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:20:25.547 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 11:20:25.548 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:289: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 11:20:25.548 [INFO] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:109: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:20:35.818 [INFO] {202957d9e841bc17bad53e413e78b022} Load into cache...
STMesh Version:  , Build Time: 
2024-03-13 15:21:28.137 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62132 connected ...
2024-03-13 15:21:28.138 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"14b5bfk7fg0czsfvx15syqw100k7sn5s","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 15:21:28.138 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "14b5bfk7fg0czsfvx15syqw100k7sn5s",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 15:21:28.138 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 15:21:28.143 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:21:28.145 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:28.345 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:28.546 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:28.747 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:28.948 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:29.149 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:29.350 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:29.551 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:29.752 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:29.955 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:30.154 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:30.355 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:30.557 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:30.758 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:30.958 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:31.160 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:31.361 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:31.562 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:31.763 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:31.966 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:32.167 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:32.368 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:32.570 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:32.774 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:32.973 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:33.174 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:33.375 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:33.577 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:33.777 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:33.978 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:21:34.180 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 2582  ...
2024-03-13 15:21:34.285 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:22:05.132 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 15:22:05.133 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:294: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 15:22:05.134 [INFO] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:23:31.860 [INFO] {18bcebed1342bc171ece61665a6c9fb1} Load into cache...
STMesh Version:  , Build Time: 
2024-03-13 15:23:35.518 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:62482 connected ...
2024-03-13 15:23:35.518 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"14b5bfk84w0czsfxjjsuvk01005vfpmd","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 15:23:35.518 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "14b5bfk84w0czsfxjjsuvk01005vfpmd",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 15:23:35.518 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 15:23:35.526 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:23:35.528 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:35.729 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:35.930 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:36.131 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:36.332 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:36.533 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:36.734 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:36.935 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:37.136 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:37.337 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:37.538 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:37.738 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:37.939 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:38.140 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:38.341 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:38.543 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:38.743 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:38.944 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:39.145 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:39.346 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:39.547 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:39.748 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:39.949 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:40.149 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:40.351 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:40.552 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:40.755 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:40.956 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:41.156 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:41.356 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:23:41.558 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 2582  ...
2024-03-13 15:23:41.649 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:24:12.516 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 15:24:12.517 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:294: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 15:24:12.519 [INFO] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:27:06.866 [INFO] {18318b024542bc175bcf7b74b1367d8e} Load into cache...
STMesh Version:  , Build Time: 
2024-03-13 15:27:10.220 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:63164 connected ...
2024-03-13 15:27:10.220 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"14b5bfk89t0czsg0a6kzs7s1001vofea","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 15:27:10.220 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "14b5bfk89t0czsg0a6kzs7s1001vofea",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 15:27:10.220 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 15:27:10.229 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:27:10.231 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:10.433 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:10.634 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:10.835 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:11.036 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:11.237 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:11.438 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:11.638 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:11.838 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:12.039 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:12.241 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:12.442 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:12.643 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:12.852 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:13.049 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:13.250 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:13.451 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:13.652 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:13.854 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:14.055 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:14.256 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:14.458 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:14.659 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:14.860 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:15.061 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:15.262 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:15.463 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:15.663 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:15.865 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:16.065 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:27:16.266 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 2582  ...
2024-03-13 15:27:16.344 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).resultHandler] stt.go:158: Recognize result: {
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:27:47.218 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 15:27:47.220 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:287: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 15:27:47.226 [INFO] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "stop",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:28:45.026 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq] websocket_man.go:37: Client [::1]:63450 connected ...
2024-03-13 15:28:45.026 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"start","call_id":"14b5bfk8c70czsg1hqhxoi81003mdqss","enable_partial":false,"route_access_code":"000","short_command":true,"vccid":"111111"}

2024-03-13 15:28:45.026 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:191: Speech to text - start: {
	"action": "start",
	"call_id": "14b5bfk8c70czsg1hqhxoi81003mdqss",
	"enable_partial": false,
	"route_access_code": "000",
	"short_command": true,
	"vccid": "111111"
}
2024-03-13 15:28:45.027 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).stt] stt.go:68: Get stt  object with vccID: 111111 routeAccessCode: 000
2024-03-13 15:28:45.028 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:28:45.029 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:45.230 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:45.432 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:45.632 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:45.834 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:46.035 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:46.235 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:46.436 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:46.636 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:46.838 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:47.038 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:47.239 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:47.441 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:47.644 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:47.844 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:48.046 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:48.246 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:48.447 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:48.648 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:48.849 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:49.050 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:49.250 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:49.452 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:49.653 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:49.853 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:50.054 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:50.256 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:50.465 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:50.665 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:50.867 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 6400  ...
2024-03-13 15:28:51.067 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).SendBuffer] stt.go:240: Send voice buffer ,buffer length 2582  ...
2024-03-13 15:28:53.030 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:115: Action Ack: {
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 15:28:53.031 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:104: msgType:1 msg:{"action":"stop"}

2024-03-13 15:28:53.031 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:287: Speech to text - stop: {
	"action": "stop"
}
2024-03-13 15:28:53.031 [INFO] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:291: Positive stopped ,then return stop ack
