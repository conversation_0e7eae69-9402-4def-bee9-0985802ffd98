2024-03-27 15:29:19.830 [INFO] {581ed7e4848ec017b6064d7d1a146631} Load into cache...
STMesh Version:  , Build Time: 
2024-03-27 15:32:21.533 [INFO] {90e39832af8ec017b7064d7df4b06ae0} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:42: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "這是一個測試的文字",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.4
}
2024-03-27 15:32:21.534 [INFO] {90e39832af8ec017b7064d7df4b06ae0} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "這是一個測試的文字",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.4
}
2024-03-27 15:32:21.545 [INFO] {90e39832af8ec017b7064d7df4b06ae0} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-03-27 15:58:12.872 [INFO] {d8879b651890c017b8064d7d80e4d2f4} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:42: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "這是一個測試的文字",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.4
}
2024-03-27 15:58:12.872 [INFO] {d8879b651890c017b8064d7d80e4d2f4} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 1,
	"text": "這是一個測試的文字",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 1.4
}
2024-03-27 15:58:12.874 [INFO] {d8879b651890c017b8064d7d80e4d2f4} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-03-27 15:58:13.764 [INFO] {d8879b651890c017b8064d7d80e4d2f4} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is wav 
2024-03-27 15:59:19.916 [INFO] {581ed7e4848ec017b6064d7d1a146631} [SonaMesh/internal/logic/tts.updateRecordFromCache] tts.go:70: Update record from cache...
2024-03-27 15:59:19.918 [INFO] {581ed7e4848ec017b6064d7d1a146631} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:106: Check voice files  expired...
2024-03-27 16:11:40.202 [INFO] {e0b7125ed490c017b9064d7dd7454308} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:42: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "程序執行中",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 0.5
}
2024-03-27 16:11:40.203 [INFO] {e0b7125ed490c017b9064d7dd7454308} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "程序執行中",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 0.5
}
2024-03-27 16:11:40.204 [INFO] {e0b7125ed490c017b9064d7dd7454308} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-03-27 16:11:52.379 [INFO] {20ada934d790c017ba064d7d33a879a6} [SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis] tts_v1_tts.go:42: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "程序執行中",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 0.5
}
2024-03-27 16:11:52.380 [INFO] {20ada934d790c017ba064d7d33a879a6} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:321: Synthesis: {
	"language": "zh-TW",
	"route_access_code": "000",
	"speed": 0.5,
	"text": "程序執行中",
	"type": "wav",
	"user_id": "",
	"vccid": "111111",
	"volume": 0.5
}
2024-03-27 16:11:52.381 [INFO] {20ada934d790c017ba064d7d33a879a6} [SonaMesh/internal/logic/tts.(*sTts).tts] tts.go:255: Get tts object with vccID: 111111 routeAccessCode: 000
2024-03-27 16:11:53.325 [INFO] {20ada934d790c017ba064d7d33a879a6} [SonaMesh/utility.WriteToFile] helper.go:32: Write to file file suffix is wav 
