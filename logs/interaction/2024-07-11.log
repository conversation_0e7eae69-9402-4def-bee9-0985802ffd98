2024-07-11 13:31:57.534 [DEBU] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027", "language": "zh-TW", "type": "wav", "speed": 6.0, "volume": 3.0, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:31:59.358 [DEBU] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:32:12.357 [DEBU] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 3.0, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:32:12.370 [DEBU] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:32:25.390 [DEBU] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027", "language": "zh-TW", "type": "wav", "speed": 0.5, "volume": 40.6, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:32:25.413 [DEBU] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:32:36.127 [DEBU] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027. ", "language": "zh-TW", "type": "wav", "speed": 0.5, "volume": 40.6, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:32:38.012 [DEBU] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:32:52.966 [DEBU] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027. test", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 40.6, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:32:54.830 [DEBU] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:47:01.706 [DEBU] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027. abc", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 40.6, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:47:03.570 [DEBU] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 13:47:30.867 [DEBU] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027. abc def", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 40.6, "user_id": "fafadf", "vccid": "111111", "route_access_code": "000"}
2024-07-11 13:47:32.690 [DEBU] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 14:32:24.016 [DEBU] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "\u53ef\u7f16\u7a0b\u6027:PostgreSQL\u5141\u8bb8\u5f00\u53d1\u4eba\u5458\u4f7f\u7528\u591a\u79cd\u7f16\u7a0b\u8bed\u8a00(\u5982PL/pgSQL\u3001PL/Python\u3001PL/Perl\u7b49)\u7f16\u5199\u5b58\u50a8\u8fc7\u7a0b\u548c\u51fd\u6570,\u589e\u5f3a\u4e86\u6570\u636e\u5e93\u7684\u7075\u6d3b\u6027\u548c\u53ef\u6269\u5c55\u6027", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 70.1, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 14:32:26.023 [DEBU] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 14:33:23.320 [DEBU] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 70.1, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 14:33:26.841 [DEBU] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 14:34:41.184 [DEBU] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 70.1, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 14:34:41.204 [DEBU] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 14:34:49.503 [DEBU] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 70.1, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 14:34:49.510 [DEBU] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 17:00:50.424 [DEBU] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 17:00:50.436 [DEBU] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:46: Response: \n {"code":-1,"message":""} 
2024-07-11 17:01:15.655 [DEBU] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": ""}
2024-07-11 17:01:15.658 [DEBU] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:46: Response: \n {"code":-1,"message":""} 
2024-07-11 17:07:57.264 [DEBU] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": ""}
2024-07-11 17:07:57.277 [DEBU] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:46: Response: \n {"code":-1,"message":""} 
2024-07-11 17:08:23.037 [DEBU] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": "000"}
2024-07-11 17:08:23.058 [DEBU] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:46: Response: \n {"code":-1,"message":""} 
2024-07-11 17:10:41.715 [DEBU] {4821501c911de1173fea5c7ec5b19e73} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "", "route_access_code": ""}
2024-07-11 17:10:41.717 [DEBU] {4821501c911de1173fea5c7ec5b19e73} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:46: Response: \n {"code":51,"message":"The VccId field is required","data":null} 
2024-07-11 17:11:22.636 [DEBU] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u6a21\u578b\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": ""}
2024-07-11 17:11:26.297 [DEBU] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
2024-07-11 17:12:02.727 [DEBU] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:39: Request-Uri [/v1/tts/] \n Body: {"text": "Azure AI \u8bed\u97f3\u670d\u52a1\u73b0\u5df2\u63a8\u51fa\u89c6\u9891\u7ffb\u8bd1\u529f\u80fd\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u89c6\u9891\u7ffb\u8bd1\uff1f\u3002\n\u4e2a\u4eba\u58f0\u97f3\u73b0\u5df2\u63a8\u51fa\u6b63\u5f0f\u7248\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f\u4e2a\u4eba\u58f0\u97f3\uff1f\u3002\nAzure AI \u8bed\u97f3\u670d\u52a1\u652f\u6301 OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\u3002 \u6709\u5173\u8be6\u7ec6\u4fe1\u606f\uff0c\u8bf7\u53c2\u9605\u4ec0\u4e48\u662f OpenAI \u6587\u672c\u8f6c\u8bed\u97f3\u58f0\u97f3\uff1f\u3002\n\u5b9a\u5236\u58f0\u97f3 API \u53ef\u7528\u4e8e\u521b\u5efa\u548c\u7ba1\u7406\u4e13\u4e1a\u548c\u4e2a\u4eba\u795e\u7ecf\u7f51\u7edc\u5b9a\u5236\u58f0\u97f3\u3002", "language": "zh-TW", "type": "wav", "speed": 1.0, "volume": 61.7, "user_id": "", "vccid": "111111", "route_access_code": ""}
2024-07-11 17:12:06.033 [DEBU] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/cmd.recordsOfInteraction] cmd.go:48: Return response ....
