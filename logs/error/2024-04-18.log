2024-04-18 09:16:34.090 [ERRO] {586e7e6ee73ac7173478f850071d498b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:52578: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:134
3.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
4.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
5.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
6.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:20:44.216 [ERRO] {90a741a5213bc717107f761203680880} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53051: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:137
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:23:15.590 [ERRO] {384088e6443bc7176914236cc4e1baf0} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53469: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:49.863 [ERRO] {e8ecd2f9703bc717eb14236c8e267c5f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53925: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:49.883 [ERRO] {204fcff9703bc717e114236c0eb17db2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53915: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:49.894 [ERRO] {e035cef9703bc717dd14236c925a1287} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53910: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.010 [ERRO] {a866f8f9703bc7170c15236c01d4255d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53952: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.010 [ERRO] {a891dcf9703bc717fb14236c900f27c8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53947: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.010 [ERRO] {1825f2f9703bc7170615236c4050fb1e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53937: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.011 [ERRO] {88f227fa703bc7171815236c097996be} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53969: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.011 [ERRO] {206149fa703bc7172d15236c690065f6} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53999: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.012 [ERRO] {f80928fa703bc7171915236c5fd025e2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53976: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.012 [ERRO] {f88c27fa703bc7171615236c66059e3b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53974: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.012 [ERRO] {a89ddaf9703bc717f714236c94e8e9e3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53944: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.013 [ERRO] {2019d8f9703bc717f114236cccd48587} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53931: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.013 [ERRO] {686bf2f9703bc7170715236c2197a6df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53938: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.015 [ERRO] {b0862afa703bc7172415236ce9678356} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53981: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.021 [ERRO] {80ebdcf9703bc717fc14236ca12eedd6} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53950: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.021 [ERRO] {d8e7f6f9703bc7170b15236c92301c93} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53954: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.024 [ERRO] {483318fa703bc7170d15236c1a895e64} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53959: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.025 [ERRO] {70f028fa703bc7171f15236cdbd3c7b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53965: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.029 [ERRO] {c8822afa703bc7172315236cbbf51821} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53993: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.040 [ERRO] {c8bc4afa703bc7173415236ca7b96c26} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53988: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.044 [ERRO] {40af49fa703bc7172f15236c0db846d8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53998: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.044 [ERRO] {28501bfa703bc7171115236c71b0591f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53964: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.058 [ERRO] {5815e5f9703bc7170015236c0583f15c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53958: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.063 [ERRO] {500c41fa703bc7172a15236ccfbe6579} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53995: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.111 [ERRO] {68032dfa703bc7172715236c4ab93b93} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53991: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.126 [ERRO] {e04149fa703bc7172c15236cdbf28225} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53983: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.129 [ERRO] {b0434afa703bc7173215236cab5b7453} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53986: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.848 [ERRO] {809a28fa703bc7171d15236c301261e9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53968: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.895 [ERRO] {7863e5f9703bc7170215236c6a7bde5e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53941: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.942 [ERRO] {702accf9703bc717dc14236c7eeaeb64} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53911: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:50.944 [ERRO] {78cb27fa703bc7171715236ccc31224e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53977: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.119 [ERRO] {60231afa703bc7170f15236c4cf40ada} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53962: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.120 [ERRO] {883c6ffa703bc7173c15236c63425c77} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54006: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.121 [ERRO] {60fd49fa703bc7173115236c2dc4ceb0} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53997: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.122 [ERRO] {a0e0d6f9703bc717ef14236ca709057e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53929: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.122 [ERRO] {f03851fa703bc7173715236c735fcb15} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54002: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.121 [ERRO] {3844e5f9703bc7170115236cbc06fe2b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53942: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.125 [ERRO] {b80ccff9703bc717e014236c930f8256} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53914: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.125 [ERRO] {608049fa703bc7172e15236c0788ac5e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53985: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.122 [ERRO] {10d71afa703bc7171015236c15d28e1f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53963: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.122 [ERRO] {1099d1f9703bc717e714236c8c1cfca9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53928: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.129 [ERRO] {500219fa703bc7170e15236cbfdcb54e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53961: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.124 [ERRO] {c03c28fa703bc7171b15236c9bbdd2a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53967: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.130 [ERRO] {502fd3f9703bc717ec14236c43e821b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53923: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.125 [ERRO] {a08dcff9703bc717e214236c6802e916} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53916: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.122 [ERRO] {e82332fa703bc7172915236c1cfca49e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53994: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.131 [ERRO] {e0551cfa703bc7171215236ccfffa1fe} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53960: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.131 [ERRO] {302edbf9703bc717f814236c2599a80a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53943: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.131 [ERRO] {40e4d8f9703bc717f414236cf49ff40c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53935: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.125 [ERRO] {20cccff9703bc717e314236c5c52d70c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53913: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.132 [ERRO] {3040d8f9703bc717f214236cce19ba4c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53932: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.130 [ERRO] {7006d2f9703bc717e814236c1777d10c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53921: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.133 [ERRO] {e8472cfa703bc7172615236ca27f5632} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53992: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.130 [ERRO] {70bdf2f9703bc7170815236c8cccd6ae} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53939: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.132 [ERRO] {60855dfa703bc7173b15236c1961abb3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54000: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.132 [ERRO] {90005dfa703bc7173a15236c79005ba1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54001: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.135 [ERRO] {e83fdaf9703bc717f614236c7508b898} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53945: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.136 [ERRO] {981928fa703bc7171a15236c9ba773b5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53972: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.134 [ERRO] {10506ffa703bc7173d15236c88990b43} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54007: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.136 [ERRO] {9076f6f9703bc7170a15236c5a4373ff} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53951: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.138 [ERRO] {30482afa703bc7172215236c283887c8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53979: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.137 [ERRO] {183ed9f9703bc717f514236c5ba7ab07} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53934: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.138 [ERRO] {487e4afa703bc7173315236c156da36e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53987: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.130 [ERRO] {90cd26fa703bc7171315236c0dc01e5d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53971: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.138 [ERRO] {981f27fa703bc7171415236cb9194266} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53973: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:51.137 [ERRO] {084dd0f9703bc717e514236c67fc04cb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53919: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:387
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 09:26:54.586 [ERRO] {40e9ecf9703bc7170415236c8d564538} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53953: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.586 [ERRO] {80c2cef9703bc717df14236cdc39b7c8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53920: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.587 [ERRO] {287f28fa703bc7171c15236cbabd4dff} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53966: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.587 [ERRO] {60b5d8f9703bc717f314236cfd8671fd} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53933: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.587 [ERRO] {a087d0f9703bc717e614236ce238cb5d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53918: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.588 [ERRO] {a01be2f9703bc717fe14236c2db33c2c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53956: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.588 [ERRO] {08a0d7f9703bc717f014236c0439b41c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53930: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.589 [ERRO] {08b8d3f9703bc717ee14236cddea6b8a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53927: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.587 [ERRO] {589fcef9703bc717de14236cc2f91f70} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53912: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.587 [ERRO] {2848c9f9703bc717db14236cea75323f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53908: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.590 [ERRO] {f81ee6f9703bc7170315236c3ccc93db} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53955: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.590 [ERRO] {681b29fa703bc7172015236c542552cb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53978: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.590 [ERRO] {c04cd2f9703bc717e914236c180acfb0} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53922: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.592 [ERRO] {c84b48fa703bc7172b15236ce6918234} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53996: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.592 [ERRO] {409ce4f9703bc717ff14236c40748478} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53946: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.593 [ERRO] {58c7f1f9703bc7170515236cd02e72b2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53936: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.590 [ERRO] {a0e828fa703bc7171e15236cab922b7c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53970: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.593 [ERRO] {08072ffa703bc7172815236cd79415ad} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53982: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.593 [ERRO] {1093d2f9703bc717ea14236c1b72de42} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53924: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.593 [ERRO] {880ed0f9703bc717e414236c45bb9526} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53917: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.595 [ERRO] {485627fa703bc7171515236caed1fc70} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53975: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.595 [ERRO] {909559fa703bc7173915236c8617cf32} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54004: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.596 [ERRO] {b871d3f9703bc717ed14236ce11bb0de} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53926: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.687 [ERRO] {48724cfa703bc7173615236cdaa51810} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:54005: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 09:26:54.687 [ERRO] {e833dcf9703bc717fa14236ccf534c35} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:53948: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:189

2024-04-18 13:56:29.908 [ERRO] {9005d706274ac71787f4317b90633e9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64104: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.912 [ERRO] {a085dd06274ac7178cf4317b829a8b95} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64123: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.924 [ERRO] {e844d106274ac71782f4317b1c9c8e92} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64121: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.936 [ERRO] {40dfa706274ac71775f4317bf5137022} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64090: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.943 [ERRO] {70491207274ac7179af4317b4cdca01f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64129: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.972 [ERRO] {d0e3cc06274ac7177df4317b66020d99} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64096: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.972 [ERRO] {08a6b906274ac71779f4317ba4167b45} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64092: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:29.972 [ERRO] {500ca706274ac71774f4317bf099d1ba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64097: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.004 [ERRO] {c8c6d806274ac71788f4317bba7ca187} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64106: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.006 [ERRO] {2023e306274ac71792f4317b5cd811a1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64128: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.036 [ERRO] {5004d206274ac71783f4317b0314f9ea} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64122: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.047 [ERRO] {08aae206274ac71791f4317b2e1e7c4e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64125: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.058 [ERRO] {b8319706274ac71772f4317b9df57965} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64099: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.080 [ERRO] {a80cea06274ac71794f4317b026d2cb4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64117: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.084 [ERRO] {10cc6707274ac717c3f4317ba0a04b98} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64169: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.084 [ERRO] {58c0ea06274ac71795f4317b544bc8ee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64114: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.112 [ERRO] {2059da06274ac7178af4317bb4a10c2e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64108: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.113 [ERRO] {d0bd7907274ac717cff4317b23c1952c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64180: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.113 [ERRO] {c8084b07274ac717bff4317b4dbc11ee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64163: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.117 [ERRO] {50fc7907274ac717d0f4317b7597a2c1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64181: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.126 [ERRO] {90936607274ac717c1f4317b8cc72e0a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64173: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.128 [ERRO] {884f3a07274ac717abf4317baa2182c1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64147: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.139 [ERRO] {30a53c07274ac717b1f4317bd330c107} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64151: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.149 [ERRO] {90021607274ac7179ff4317b704d3eb3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64133: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.150 [ERRO] {58c1d506274ac71785f4317b1d4338dd} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64109: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.157 [ERRO] {b012cd06274ac7177ef4317b97806b86} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64105: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.158 [ERRO] {08f42907274ac717a1f4317bd13319e1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64136: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.158 [ERRO] {b8234007274ac717bbf4317b38ac5f80} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64143: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.169 [ERRO] {a0ac1507274ac7179ef4317b7caaba49} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64132: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.169 [ERRO] {284b6707274ac717c2f4317b39c859a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64168: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.189 [ERRO] {10573c07274ac717b0f4317b92db502d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64162: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.200 [ERRO] {a0966c07274ac717c6f4317bb5265364} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64172: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.206 [ERRO] {88ba3d07274ac717b6f4317bb80edeea} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64153: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.235 [ERRO] {e0583d07274ac717b4f4317beac83b86} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64165: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.240 [ERRO] {a8853e07274ac717b7f4317b7cd19a92} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64154: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.247 [ERRO] {805e1507274ac7179df4317b39d1fa9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64131: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.264 [ERRO] {88de3707274ac717a5f4317b18915881} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64138: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.264 [ERRO] {d875ec06274ac71796f4317b7f400014} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64116: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.274 [ERRO] {00d73507274ac717a3f4317b2d2089c0} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64144: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.281 [ERRO] {404b1307274ac7179bf4317b30da5810} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64134: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.302 [ERRO] {30de7107274ac717cbf4317bd1c491c6} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64179: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.304 [ERRO] {f86c3907274ac717a8f4317bd669c8ff} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64146: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.308 [ERRO] {e879dd06274ac7178bf4317befab0534} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64126: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.312 [ERRO] {58419706274ac71773f4317b28bc8ac1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64098: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.323 [ERRO] {90b3ba06274ac7177cf4317bfa0f444c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64095: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.334 [ERRO] {28f4b906274ac7177af4317bce38b6ea} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64093: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.335 [ERRO] {48303a07274ac717aaf4317b84fec26d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64141: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.360 [ERRO] {20843b07274ac717adf4317b83152830} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64149: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.363 [ERRO] {40d23b07274ac717aef4317b9d73b009} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64150: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.370 [ERRO] {68d74007274ac717bdf4317b74f84356} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64142: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.378 [ERRO] {e86ddf06274ac7178ef4317b2095a1e4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64110: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.382 [ERRO] {20ef3e07274ac717b8f4317ba953d8c3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64155: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.391 [ERRO] {98e73c07274ac717b2f4317b4e3d7ea4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64164: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.402 [ERRO] {08af7307274ac717cef4317b709305cb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64178: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.402 [ERRO] {1038d906274ac71789f4317bcea329f0} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64107: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.402 [ERRO] {3821b906274ac71778f4317bc8162a13} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64091: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.413 [ERRO] {30283c07274ac717aff4317bae2ecfe5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64161: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.428 [ERRO] {70986d07274ac717c7f4317b86e60045} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64174: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.431 [ERRO] {a8d6f206274ac71797f4317bc591d58d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64113: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.434 [ERRO] {f007d406274ac71784f4317ba568e21b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64100: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.436 [ERRO] {98ea7a07274ac717d2f4317bdb3e0020} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64186: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.442 [ERRO] {d0544c07274ac717c0f4317bc5e06d4f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64160: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.463 [ERRO] {80033907274ac717a6f4317bcb341c82} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64145: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.494 [ERRO] {f84dd606274ac71786f4317bec608e59} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64103: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.510 [ERRO] {d0cbd006274ac71781f4317b53d6f96f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64120: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.908 [ERRO] {70730b07274ac71798f4317be96a6b6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64130: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.921 [ERRO] {e8cdcf06274ac71780f4317bd7a71a78} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64119: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:30.928 [ERRO] {50d36b07274ac717c5f4317b37e25058} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64171: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.136 [ERRO] {88356807274ac717c4f4317b1908df56} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64170: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.143 [ERRO] {a88e7b07274ac717d4f4317b8bee8b12} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64184: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.143 [ERRO] {f0023d07274ac717b3f4317b433e0ac1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64152: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.166 [ERRO] {b0843707274ac717a4f4317bfa1eed6d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64167: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.166 [ERRO] {686e9606274ac71771f4317b0e467143} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64088: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.173 [ERRO] {10522807274ac717a0f4317b1217a8aa} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64135: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.174 [ERRO] {e0224607274ac717bef4317b0ca76f59} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64158: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.201 [ERRO] {685a4007274ac717bcf4317b74d2f75a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64159: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.229 [ERRO] {88c37a07274ac717d1f4317b5a85211c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64183: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.249 [ERRO] {70156e07274ac717c9f4317b2862222b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64176: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.448 [ERRO] {206c3f07274ac717b9f4317bba503631} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64156: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.572 [ERRO] {e8aa3d07274ac717b5f4317bbf1f02ba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64166: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.584 [ERRO] {e83b1107274ac71799f4317b7cb3b5fe} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64111: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:31.600 [ERRO] {2871ba06274ac7177bf4317b265d19c3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64089: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.102 [ERRO] {00261407274ac7179cf4317b68301efa} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64115: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.135 [ERRO] {38a4b806274ac71777f4317bc2b1685c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64094: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.160 [ERRO] {708ddd06274ac7178df4317b327f524d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64102: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.216 [ERRO] {a073e006274ac7178ff4317bde296d4e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64127: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.358 [ERRO] {b86e7207274ac717ccf4317bc6b3e5b4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64187: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.424 [ERRO] {88553907274ac717a7f4317bec3fe9ec} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64140: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.444 [ERRO] {60a1e806274ac71793f4317ba41a5bca} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64112: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.465 [ERRO] {202bb806274ac71776f4317ba040d91e} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64101: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:32.752 [ERRO] {70156e07274ac717caf4317bdf078604} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64175: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:32.951 [ERRO] {78223b07274ac717acf4317b1a986811} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64148: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:32.956 [ERRO] {40827207274ac717cdf4317b173c871a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64182: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:33.185 [ERRO] {d0a32a07274ac717a2f4317b6724e7b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64137: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:33.218 [ERRO] {70156e07274ac717caf4317bdf078604} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64175: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:33.332 [ERRO] {e8016e07274ac717c8f4317bcdaf6681} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64177: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:33.382 [ERRO] {78223b07274ac717acf4317b1a986811} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64148: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:33.479 [ERRO] {40827207274ac717cdf4317b173c871a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64182: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:33.749 [ERRO] {e0c93f07274ac717baf4317b408a5586} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64157: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:33.749 [ERRO] {90157b07274ac717d3f4317bd82cc9d2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64185: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:33.949 [ERRO] {d8b7e106274ac71790f4317bde40be09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64124: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:34.136 [ERRO] {90157b07274ac717d3f4317bd82cc9d2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64185: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:34.214 [ERRO] {e0c93f07274ac717baf4317b408a5586} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64157: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:34.414 [ERRO] {d8b7e106274ac71790f4317bde40be09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64124: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:38.950 [ERRO] {c0aacf06274ac7177ff4317bbe719875} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64118: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:39.459 [ERRO] {c0aacf06274ac7177ff4317bbe719875} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64118: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

2024-04-18 13:56:40.649 [ERRO] {d0c63907274ac717a9f4317b299c8abc} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64139: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-04-18 13:56:40.920 [ERRO] {d0c63907274ac717a9f4317b299c8abc} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:67: write tcp 127.0.0.1:8000->127.0.0.1:64139: write: broken pipe 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:67
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:139
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:133
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2040

