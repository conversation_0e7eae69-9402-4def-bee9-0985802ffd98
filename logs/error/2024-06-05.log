2024-06-05 10:39:45.088 [ERRO] {60aa246286fad517a503dd7f9b078279} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Stop action ack timeout 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:212

2024-06-05 10:39:45.089 [ERRO] {5088386286fad517b203dd7f94d2e255} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Stop action ack timeout 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:212

2024-06-05 10:39:45.091 [ERRO] {f0bb326286fad517b003dd7fa458d8b5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Stop action ack timeout 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:212

2024-06-05 10:39:45.169 [ERRO] {c887206286fad5179f03dd7f95b181d9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Stop action ack timeout 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:212

2024-06-05 10:39:45.169 [ERRO] {c0e12d6286fad517ad03dd7f98458d75} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Stop action ack timeout 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:212

2024-06-05 11:01:02.698 [ERRO] {98f4729170fcd517abfb0f6681dbf7d6} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.700 [ERRO] {98f4729170fcd517abfb0f6681dbf7d6} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.699 [ERRO] {58fe809170fcd517b1fb0f6603c9c5d5} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.702 [ERRO] {58fe809170fcd517b1fb0f6603c9c5d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.707 [ERRO] {588d7e9170fcd517b0fb0f66586da1dd} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.711 [ERRO] {588d7e9170fcd517b0fb0f66586da1dd} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.708 [ERRO] {084d7d9170fcd517aefb0f66cd8cbd41} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.715 [ERRO] {084d7d9170fcd517aefb0f66cd8cbd41} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.708 [ERRO] {50c47c9170fcd517adfb0f66d14fcc18} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.721 [ERRO] {50c47c9170fcd517adfb0f66d14fcc18} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.725 [ERRO] {d0d2849170fcd517b2fb0f66e498ce6c} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.725 [ERRO] {d0d2849170fcd517b2fb0f66e498ce6c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:01:02.766 [ERRO] {8004719170fcd517a6fb0f663feee00c} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:01:02.766 [ERRO] {8004719170fcd517a6fb0f663feee00c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.906 [ERRO] {705f859170fcd517b4fb0f66a07fea19} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.906 [ERRO] {987d719170fcd517a9fb0f66f1a518ec} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.906 [ERRO] {705a719170fcd517a8fb0f66ea413969} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.906 [ERRO] {f8f5849170fcd517b3fb0f664ae497be} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.914 [ERRO] {987d719170fcd517a9fb0f66f1a518ec} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.906 [ERRO] {48a8739170fcd517acfb0f6625157edb} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.908 [ERRO] {705f859170fcd517b4fb0f66a07fea19} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.914 [ERRO] {705a719170fcd517a8fb0f66ea413969} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.914 [ERRO] {f8f5849170fcd517b3fb0f664ae497be} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.915 [ERRO] {48a8739170fcd517acfb0f6625157edb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:06:46.921 [ERRO] {2865869170fcd517b6fb0f668445b6dc} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:06:46.921 [ERRO] {2865869170fcd517b6fb0f668445b6dc} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:08:17.431 [ERRO] {4837719170fcd517a7fb0f6639577692} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:08:17.431 [ERRO] {4837719170fcd517a7fb0f6639577692} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:08:17.490 [ERRO] {a0d5709170fcd517a5fb0f6692594eec} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:08:17.491 [ERRO] {a0d5709170fcd517a5fb0f6692594eec} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:08:17.632 [ERRO] {383f7e9170fcd517affb0f66c5dea342} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:08:17.632 [ERRO] {383f7e9170fcd517affb0f66c5dea342} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:09:54.313 [ERRO] {90a8719170fcd517aafb0f663d330046} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:09:54.313 [ERRO] {380f869170fcd517b5fb0f66f0114580} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:09:54.314 [ERRO] {b8d1709170fcd517a4fb0f66b62b0fca} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:09:54.314 [ERRO] {b078869170fcd517b7fb0f66e9790ace} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:09:54.422 [ERRO] {380f869170fcd517b5fb0f66f0114580} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:09:54.433 [ERRO] {b078869170fcd517b7fb0f66e9790ace} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:09:54.435 [ERRO] {b8d1709170fcd517a4fb0f66b62b0fca} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:09:54.439 [ERRO] {90a8719170fcd517aafb0f663d330046} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:21:48.020 [ERRO] {78d5648cfffbd5178afb0f66cd95c35f} [SonaMesh/internal/logic/tts.checkVoiceFilesExpired] tts.go:119: os.Open failed for name "./voc": open ./voc: no such file or directory 
Stack:
1.  SonaMesh/internal/logic/tts.checkVoiceFilesExpired
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:119
2.  SonaMesh/internal/logic/tts.updateRecordFromCache
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:73

2024-06-05 11:23:43.520 [ERRO] {e878f26dadfdd517ec7c0250d17235dd} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.523 [ERRO] {e878f26dadfdd517ec7c0250d17235dd} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.538 [ERRO] {080efc6dadfdd517f87c0250729eebad} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.538 [ERRO] {080efc6dadfdd517f87c0250729eebad} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.538 [ERRO] {b0ecfc6dadfdd517f97c0250fbc4a69c} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.540 [ERRO] {b0ecfc6dadfdd517f97c0250fbc4a69c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.539 [ERRO] {2034f56dadfdd517f07c025055ceb35d} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.541 [ERRO] {2034f56dadfdd517f07c025055ceb35d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.589 [ERRO] {e020f36dadfdd517ee7c0250eb9f407a} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.589 [ERRO] {3025fe6dadfdd517fd7c02504d33f845} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.590 [ERRO] {3025fe6dadfdd517fd7c02504d33f845} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.589 [ERRO] {e020f36dadfdd517ee7c0250eb9f407a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:23:43.590 [ERRO] {40dbfb6dadfdd517f77c02505c1cad9f} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:461: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:461
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:23:43.592 [ERRO] {40dbfb6dadfdd517f77c02505c1cad9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:28:00.235 [ERRO] {40e1fa6dadfdd517f57c0250c0c90499} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.235 [ERRO] {c08afe6dadfdd517fe7c0250ba6d2e55} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.235 [ERRO] {50bbf26dadfdd517ed7c02501769e5f9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.235 [ERRO] {30d8f56dadfdd517f17c02507cae603c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.235 [ERRO] {30a8fd6dadfdd517fb7c025036a7baf3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.235 [ERRO] {7849f66dadfdd517f27c0250613f6aa9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:00.337 [ERRO] {7849f66dadfdd517f27c0250613f6aa9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:00.342 [ERRO] {30d8f56dadfdd517f17c02507cae603c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:00.350 [ERRO] {40e1fa6dadfdd517f57c0250c0c90499} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:00.350 [ERRO] {30a8fd6dadfdd517fb7c025036a7baf3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:00.350 [ERRO] {50bbf26dadfdd517ed7c02501769e5f9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:00.358 [ERRO] {c08afe6dadfdd517fe7c0250ba6d2e55} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.233 [ERRO] {e0d8fe6dadfdd517ff7c02506a59be24} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.234 [ERRO] {2093fa6dadfdd517f47c0250db227457} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.233 [ERRO] {e836fd6dadfdd517fa7c0250eaa90e13} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.234 [ERRO] {b0e6fd6dadfdd517fc7c025070986d38} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.234 [ERRO] {a04efb6dadfdd517f67c025076451662} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.235 [ERRO] {b093f66dadfdd517f37c025017e99782} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.235 [ERRO] {4863f36dadfdd517ef7c0250eab3055b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-05 11:28:03.427 [ERRO] {b0e6fd6dadfdd517fc7c025070986d38} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.436 [ERRO] {e0d8fe6dadfdd517ff7c02506a59be24} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.437 [ERRO] {2093fa6dadfdd517f47c0250db227457} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.455 [ERRO] {a04efb6dadfdd517f67c025076451662} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.475 [ERRO] {b093f66dadfdd517f37c025017e99782} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.475 [ERRO] {e836fd6dadfdd517fa7c0250eaa90e13} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:28:03.483 [ERRO] {4863f36dadfdd517ef7c0250eab3055b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:405
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:37:20.311 [ERRO] {60ab55926bfed517753cf23f08c8e4ef} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:466: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:466
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:37:20.317 [ERRO] {60ab55926bfed517753cf23f08c8e4ef} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:37:20.311 [ERRO] {783c52926bfed517683cf23f75bee6ff} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:466: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:466
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:37:20.319 [ERRO] {783c52926bfed517683cf23f75bee6ff} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:37:20.365 [ERRO] {705555926bfed517743cf23fce8285d2} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:466: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:466
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:37:20.365 [ERRO] {705555926bfed517743cf23fce8285d2} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:37:20.367 [ERRO] {e0425c926bfed517793cf23f0dfa375a} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:466: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:466
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:37:20.367 [ERRO] {c8f354926bfed517723cf23fb3035c09} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:466: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:466
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:255
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-06-05 11:37:20.370 [ERRO] {e0425c926bfed517793cf23f0dfa375a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:37:20.381 [ERRO] {c8f354926bfed517723cf23fb3035c09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-06-05 11:38:05.860 [ERRO] {f09f53926bfed5176d3cf23ff9fd0e71} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.861 [ERRO] {40d456926bfed517763cf23f7d0c052f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.863 [ERRO] {184c52926bfed5176a3cf23fa4f866bb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.865 [ERRO] {980154926bfed5176f3cf23f9c32063f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.866 [ERRO] {e07853926bfed5176c3cf23f9a6f2626} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.869 [ERRO] {e0ef54926bfed517703cf23f9997e497} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.870 [ERRO] {f0755a926bfed517783cf23f806b8c10} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:05.871 [ERRO] {f09f53926bfed5176e3cf23fe2c9f63d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:06.230 [ERRO] {980154926bfed5176f3cf23f9c32063f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.232 [ERRO] {184c52926bfed5176a3cf23fa4f866bb} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.232 [ERRO] {f09f53926bfed5176d3cf23ff9fd0e71} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.270 [ERRO] {f09f53926bfed5176e3cf23fe2c9f63d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.276 [ERRO] {f0755a926bfed517783cf23f806b8c10} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.277 [ERRO] {40d456926bfed517763cf23f7d0c052f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.282 [ERRO] {e07853926bfed5176c3cf23f9a6f2626} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:06.286 [ERRO] {e0ef54926bfed517703cf23f9997e497} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:15.860 [ERRO] {68e559926bfed517773cf23fc57104d9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.863 [ERRO] {184653926bfed5176b3cf23f51e7a14a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.868 [ERRO] {783c52926bfed517693cf23fd0fc8d74} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.878 [ERRO] {d81a55926bfed517733cf23f67215c85} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.897 [ERRO] {e0ef54926bfed517713cf23f39f0ed71} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.897 [ERRO] {a01d5d926bfed5177a3cf23f083206f4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:15.898 [ERRO] {783c52926bfed517673cf23f1e2d31a1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:195

2024-06-05 11:38:16.121 [ERRO] {d81a55926bfed517733cf23f67215c85} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.131 [ERRO] {783c52926bfed517693cf23fd0fc8d74} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.133 [ERRO] {a01d5d926bfed5177a3cf23f083206f4} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.141 [ERRO] {783c52926bfed517673cf23f1e2d31a1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.143 [ERRO] {184653926bfed5176b3cf23f51e7a14a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.144 [ERRO] {e0ef54926bfed517713cf23f39f0ed71} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-05 11:38:16.165 [ERRO] {68e559926bfed517773cf23fc57104d9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:410
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

