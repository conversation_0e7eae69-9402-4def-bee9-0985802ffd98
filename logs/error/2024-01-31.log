2024-01-31 13:43:19.453 [ERRO] {f87506263c58af171eacff3e4dc4cec9} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:165: System error: rpc error: code = Unimplemented desc = unknown service service.StreamService 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:165
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-31 14:42:58.433 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer] cyberon_stt.go:507: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_stt.go:507
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:219
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:97

2024-01-31 14:42:58.433 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:63: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:63
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:100

2024-01-31 14:42:58.434 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer] cyberon_stt.go:507: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_stt.go:507
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:219
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:97

2024-01-31 14:42:58.435 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:63: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:63
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:100

2024-01-31 14:42:58.436 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer] cyberon_stt.go:507: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_stt.go:507
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:219
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:97

2024-01-31 14:42:58.436 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:63: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:63
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:100

2024-01-31 14:42:58.439 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer] cyberon_stt.go:507: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_stt.go:507
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:219
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:97

2024-01-31 14:42:58.439 [ERRO] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:63: System error: websocket: close sent 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:63
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:100

