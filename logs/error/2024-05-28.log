2024-05-28 15:37:56.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:01.899 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:06.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:11.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:16.904 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:21.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:23.002 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:85: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:85
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:175
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:170
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-05-28 15:38:23.381 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:85: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:85
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:157
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:151
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-05-28 15:38:26.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:31.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:36.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:41.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:46.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:51.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:38:56.902 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:01.901 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:06.901 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:11.902 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:16.900 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:21.901 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:26.902 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:39:31.901 [ERRO] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:45:17.889 [ERRO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:45:42.892 [ERRO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:86: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:86
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:176
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:171
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-05-28 15:45:43.103 [ERRO] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:86: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:86
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:158
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-05-28 15:47:20.590 [ERRO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:47:43.593 [ERRO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:86: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:86
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:176
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:171
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-05-28 15:47:43.757 [ERRO] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:86: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:86
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:158
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:152
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-05-28 15:49:23.491 [ERRO] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3] websocket_man.go:65: Ping error : set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan).WebSocketReq.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:65

2024-05-28 15:51:35.952 [ERRO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:90: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:90
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:180
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:175
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-05-28 15:51:36.086 [ERRO] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:90: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:90
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:162
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:156
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-05-28 15:53:48.083 [ERRO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:94: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:94
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-05-28 15:53:48.331 [ERRO] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:94: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:94
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

