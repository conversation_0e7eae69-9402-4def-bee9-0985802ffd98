2024-12-02 15:14:58.114 [ERRO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:79: The key ,region is not retrieved...  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:79
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:79: The key ,region is not retrieved...  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:79
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:79: The key ,region is not retrieved...  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:79
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:80:  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:80
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:80:  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:80
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:80:  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:80
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/azure.(*STT).Start.func2] azure_stt.go:163: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Start.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:163
2.  SonaMesh/internal/logic/azure.(*STT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:175
3.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:214
4.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/azure.(*STT).Start.func2] azure_stt.go:163: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Start.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:163
2.  SonaMesh/internal/logic/azure.(*STT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:175
3.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:214
4.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/azure.(*STT).Start.func2] azure_stt.go:163: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Start.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:163
2.  SonaMesh/internal/logic/azure.(*STT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:175
3.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:214
4.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:14:58.114 [ERRO] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:197

2024-12-02 15:14:58.114 [ERRO] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:197

2024-12-02 15:14:58.115 [ERRO] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: Azure related parameters are not initialized 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:197

2024-12-02 15:19:23.304 [ERRO] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:79: The key ,region is not retrieved...  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).Connect
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:79
2.  SonaMesh/internal/logic/stt.(*sSTT).Start
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:205
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:155

2024-12-02 15:24:49.641 [ERRO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:151
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:222

2024-12-02 15:24:50.032 [ERRO] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:166
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:458
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-12-02 15:25:15.643 [ERRO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:184
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:179
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:151
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:222

2024-12-02 15:25:15.851 [ERRO] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:166
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:160
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:166
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:458
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-12-02 15:30:05.184 [ERRO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:519: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:519
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:262
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-12-02 15:30:05.185 [ERRO] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: System error: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-12-02 15:38:27.445 [ERRO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:522: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:522
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:262
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-12-02 15:38:27.446 [ERRO] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: System error: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

2024-12-02 15:38:27.644 [ERRO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:522: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:522
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:262
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:128

2024-12-02 15:38:27.644 [ERRO] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: System error: Exception with an error code: 0x5 (SPXERR_INVALID_ARG) 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:131

