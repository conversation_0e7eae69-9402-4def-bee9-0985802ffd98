2024-03-27 15:32:24.663 [ERRO] {90e39832af8ec017b7064d7df4b06ae0} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:154: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:154
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:343
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:46
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:47

2024-03-27 16:11:43.405 [ERRO] {e0b7125ed490c017b9064d7dd7454308} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:154: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:154
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:343
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:46
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:47

