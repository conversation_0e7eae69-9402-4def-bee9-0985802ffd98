2024-01-19 11:39:20.752 [ERRO] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:53971->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 11:41:26.860 [ERRO] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:54204->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 11:42:41.227 [ERRO] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:54356->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 11:43:07.880 [ERRO] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:54420->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 11:44:01.637 [ERRO] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 61.222.43.4:20888: i/o timeout" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 12:04:21.539 [ERRO] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 61.222.43.4:28088: i/o timeout" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 13:49:05.970 [ERRO] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:54506->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 13:49:30.369 [ERRO] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:54717->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 15:26:53.774 [ERRO] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:61760->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 15:32:10.037 [ERRO] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:151: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:151
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 15:36:39.745 [ERRO] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:151: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: read tcp 127.0.0.1:62530->127.0.0.1:7890: read: connection reset by peer" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:151
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-19 15:39:50.086 [ERRO] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:151: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:151
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

