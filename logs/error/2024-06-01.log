2024-06-01 18:56:56.525 [ERRO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer] azure_stt.go:460: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).SendVoiceBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:460
2.  SonaMesh/internal/logic/stt.(*sSTT).SendBuffer
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:254
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:129

2024-06-01 18:56:56.527 [ERRO] {b8284a8216dcd417d96cb77630aa01af} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: The stream is not initialized  
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:132

2024-06-01 19:00:33.167 [ERRO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:185
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:180
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-01 19:00:33.454 [ERRO] {985d498216dcd417d86cb77627cd2eb8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:167
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:161
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

2024-06-01 19:00:43.168 [ERRO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:185
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:180
4.  SonaMesh/internal/logic/stt.(*sSTT).ackHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:145
5.  SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:190

2024-06-01 19:00:43.299 [ERRO] {a807498216dcd417d76cb7761dc43671} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2] websocket_man.go:95: set tcp [::1]:8000: use of closed network connection 
Stack:
1.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func2
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:95
2.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3.1
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:167
3.  SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3
    /Users/<USER>/Sources/sonamesh/internal/logic/websocket_man/websocket_man.go:161
4.  SonaMesh/internal/logic/stt.(*sSTT).resultHandler
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:160
5.  SonaMesh/internal/logic/azure.(*STT).recognized
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:404
6.  github.com/Microsoft/cognitive-services-speech-sdk-go/speech.recognizerFireEventRecognized
    /Users/<USER>/go/pkg/mod/github.com/!microsoft/cognitive-services-speech-sdk-go@v1.33.0/speech/callback_helpers.go:138
7.  _cgoexp_c0b4197be0b1_recognizerFireEventRecognized
    _cgo_gotypes.go:2044

