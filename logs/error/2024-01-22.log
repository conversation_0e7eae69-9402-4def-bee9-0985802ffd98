2024-01-22 10:48:34.382 [ERRO] {708a091f798bac17e2cee20b8c5a8684} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:151: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:151
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 10:51:44.416 [ERRO] {8889735da58bac17d85a9d713bb69631} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 10:55:12.245 [ERRO] {e0bd6327cd8bac17190673727231f203} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:153: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: context deadline exceeded" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:153
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 10:58:05.245 [ERRO] {f87feca2da8bac1763ed19013c3e4b12} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:153: rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:7890: i/o timeout" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:153
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 11:05:54.994 [ERRO] {88e234686b8cac17fc82fa0bc54e4992} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:153: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:153
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 11:07:00.737 [ERRO] {c09aebb67a8cac171659de0d54d4390e} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:153: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:153
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-22 11:10:03.076 [ERRO] {30d8bd2b9f8cac17e358fa5f10f496f8} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:153: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: context deadline exceeded" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:153
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

