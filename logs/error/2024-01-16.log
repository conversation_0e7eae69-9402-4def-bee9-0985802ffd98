2024-01-16 15:13:56.799 [ERRO] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:595: exit status 1 
Stack:
1.  SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp
    /Users/<USER>/Sources/sonamesh/internal/logic/emotibot/emotibot_stt.go:595
2.  SonaMesh/internal/logic/emotibot.(*STT).RecognizeFile
    /Users/<USER>/Sources/sonamesh/internal/logic/emotibot/emotibot_stt.go:436
3.  SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:289
4.  SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile
    /Users/<USER>/Sources/sonamesh/internal/controller/stt/stt_v1_stt.go:51
5.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-16 16:17:15.739 [ERRO] {88aa8030eec5aa17825c761f6f6e0efd} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func1] azure_stt.go:431: Exception with an error code: 0xa (SPXERR_INVALID_HEADER) 
Stack:
1.  SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func1
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:431
2.  SonaMesh/internal/logic/azure.(*STT).RecognizeFile
    /Users/<USER>/Sources/sonamesh/internal/logic/azure/azure_stt.go:458
3.  SonaMesh/internal/logic/stt.(*sSTT).RecognizeFile
    /Users/<USER>/Sources/sonamesh/internal/logic/stt/stt.go:289
4.  SonaMesh/internal/controller/stt.(*ControllerV1).RecognizeAudioFile
    /Users/<USER>/Sources/sonamesh/internal/controller/stt/stt_v1_stt.go:51
5.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

2024-01-16 16:40:29.282 [ERRO] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:152: rpc error: code = Unavailable desc = connection error: desc = "transport: authentication handshake failed: EOF" 
Stack:
1.  SonaMesh/internal/logic/cyberon.(*TTS).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/cyberon/cyberon_tts.go:152
2.  SonaMesh/internal/logic/tts.(*sTts).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/logic/tts/tts.go:336
3.  SonaMesh/internal/controller/tts.(*ControllerV1).Synthesis
    /Users/<USER>/Sources/sonamesh/internal/controller/tts/tts_v1_tts.go:22
4.  SonaMesh/internal/cmd.recordsOfInteraction
    /Users/<USER>/Sources/sonamesh/internal/cmd/cmd.go:49

