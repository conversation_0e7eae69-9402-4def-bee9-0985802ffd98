2024-04-01 11:41:58.724 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"ack": "INIT",
	"message": {
		"type": "ack"
	},
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:03.556 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 1.89,
		"elapsed": 224,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -0.81,
				"transcript": "麻煩你好"
			}
		],
		"rtf": 0.12,
		"score": 0,
		"segment_info": {
			"idx": 0,
			"sentence_duration": 1.53,
			"start_time": 3.2,
			"total_duration": 4.78
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:11.558 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 6.3,
		"elapsed": 699,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -9.51,
				"transcript": "小姐我想要反應一下我買那個整本的那個筆記本後面有咖啡券嘛是"
			}
		],
		"rtf": 0.11,
		"score": 0,
		"segment_info": {
			"idx": 1,
			"sentence_duration": 5.94,
			"start_time": 6.56,
			"total_duration": 12.78
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:13.229 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 0.96,
		"elapsed": 119,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -0.19,
				"transcript": "那"
			}
		],
		"rtf": 0.12,
		"score": 0,
		"segment_info": {
			"idx": 2,
			"sentence_duration": 0.59,
			"start_time": 13.67,
			"total_duration": 14.38
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:21.674 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 8.31,
		"elapsed": 1048,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.97,
				"transcript": "你們那個日期啊是這麼小我以為是到十二月三十一號結果是到十二月三十號耶"
			}
		],
		"rtf": 0.13,
		"score": 0,
		"segment_info": {
			"idx": 3,
			"sentence_duration": 7.97,
			"start_time": 14.6,
			"total_duration": 22.78
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:26.615 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.8,
		"elapsed": 602,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -2.42,
				"transcript": "對因為我們往年每一年的券他都是到十二月三十號"
			}
		],
		"rtf": 0.13,
		"score": 0,
		"segment_info": {
			"idx": 4,
			"sentence_duration": 4.43,
			"start_time": 23.03,
			"total_duration": 27.78
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:31.041 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.02,
		"elapsed": 488,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -1.68,
				"transcript": "所以券的使用期限確實是到十二月三十"
			}
		],
		"rtf": 0.12,
		"score": 0,
		"segment_info": {
			"idx": 5,
			"sentence_duration": 3.66,
			"start_time": 28.13,
			"total_duration": 32.18
		}
	},
	"service": "recognize",
	"session_id": "1joxdy21vepd08h47mlxk14100kya5xn_111111_IcE12h",
	"status": 0
}
2024-04-01 11:42:45.437 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:138: Receive response: {
	"message": {
		"type": "error"
	},
	"status": 102
}
2024-04-01 11:42:45.438 [DEBU] {d05edb9e030bc21748d9ec418f24596e} [SonaMesh/internal/logic/emotibot.(*STT).onMessage] emotibot_stt.go:146: Recevive error ... 
