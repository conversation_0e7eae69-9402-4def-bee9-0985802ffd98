2024-01-16 14:25:25.234 [DEBU] {e88791c6d3bfaa17661eb113bc0f41d4} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=b4tHKcPE4a9YycrD&call-id=vKde7H6SIu3RjQwE&request-id=AWu44JsDxogg2Pjv
2024-01-16 14:25:28.622 [DEBU] {e88791c6d3bfaa17661eb113bc0f41d4} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:647: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 16.15,
		"elapsed": 1837,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -1.03,
				"transcript": "特殊處理了。"
			}
		],
		"rtf": 0.11,
		"score": 0
	},
	"service": "recognize",
	"session_id": "vKde7H6SIu3RjQwE_AWu44JsDxogg2Pjv_b4tHKcPE4a9YycrD",
	"status": 0
}
2024-01-16 14:26:34.925 [DEBU] {50e1ee00e4bfaa173f47130130aec776} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=MugofK0mMa8HOW1u&call-id=4PBkFcyh5XkpGG7A&request-id=fs1ydhSeTp1kFGDT
2024-01-16 14:26:36.768 [DEBU] {50e1ee00e4bfaa173f47130130aec776} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:647: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 16.15,
		"elapsed": 1049,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -1.03,
				"transcript": "特殊處理了。"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "4PBkFcyh5XkpGG7A_fs1ydhSeTp1kFGDT_MugofK0mMa8HOW1u",
	"status": 0
}
2024-01-16 14:36:41.919 [DEBU] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?do-punctuation=true&auto-digit-convert=true&sample-rate=16000&user-id=5yQPffhV4oAUK0wa&call-id=VAWtTfI1O9LQTNCd&request-id=6hG7ggeXRgEWrPjG
2024-01-16 14:38:30.288 [DEBU] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 545.09,
		"elapsed": 95637,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -3460.97,
				"transcript": "先班說有中心您好敝姓您高姓為您喂您好嘿你好，一我一次收到你們這個簡訊是什麼意思，蛤登錄平臺嗎，對呃今年創業及啓動金額金貸融零貸款樓對呀，這個就是你你不是貨拿嘿對然後勒你要登入那個融資平臺去登錄您公司的前一年度的營業額跟員工人數啊先生你等湖我姓修邱先生公司是新給我笑喔誒四二九八三十三任四二九八三四三二三四三二我我五原遠忘記這是什麼東西的就是你上貸款啊喔就是貸款一百萬那個對啊，你旁業機使中金貸款嗎，您公司是繁榮有限公司嗎，嘿對對你是負責人本人嗎，嘿對那我還用現在哪時候貨代清創的貨代啊，我有點忘記了餒抱歉因為中京我我今年去住了兩次，願有點記憶力有點好對ok，所以你就是收到那個通知對一晶對對啊我我一直奇怪啊，這是什麼東西，我一直想不起來啊，你是哪一些銀行申貸呃在的呃臺灣銀行啊分行呢，呃中信分行臺銀的中心，中信信村分行嗎，對對對中京新村好，所以你忘記哪收貨代，反正你就是收到那個平臺就是通知的簡訊，那先生那我就是請你要到平臺去登入這個資料啊，他說五月三十一號點料登錄啊，你現在如果說可以的話你就現在登錄因為我號，你會忘你要我用你叫我用好不好你你就是照那個平臺裡面之後，那手機點就可以了，是不是如果你手機可以用的話喔我否我現在我現在有收來n然後我第一次登入是按第一次還是我對過對第一次登錄對好了，那輸入你的統編等一下然後他會傳驗證碼到你的手機好傳怎嗎到我手機你已經登入過了誒基本在先完你登入葉面抄那你那你就是回到我已經登錄過那個案件誒他現在跳說哼嗯還正要我輸入那個餒桶編跟密碼餒啊密碼你記得嗎，試看看好不好嗯是我的訂話號碼吧密碼服務那前著你你密碼就是你先輸入同編個密碼然後按登錄密碼，輸預算，不然你按靜下方一個案忘記密碼他不是有有一個忘記密碼選項嗎，我我從我重重新編去裡連連結嗯好我現在現在選要寫現我一直登入過了嗎，對啊對我已經的路過然後欸一樣誒，他是要一樣的統編跟密碼勒對呀對啊，你就輸入啊，那你密碼是忘記的意思嗎，對三次三二然後選下面忘記密碼對然後，他請你要輸入喔統編跟手機對然後，你看你可不可以收到那個簡訊密碼與發動是手機，那你看一下簡訊有沒有收到喔有了我自己穿那麼大段的是英文字喔對啊，那你就你先進去然後，你只要去改你你比較好記的密碼應該是你等我一下好不好嗯啊你我現下去，現在你的畫面是到哪裡啊，你現在的畫面等是怎樣他，他他現在我現在要手法半頁然後蘇旁編我想樣查我看能不能貼上我掃發他不不隔貼上先不是密集亂碼誒，那你之前都沒有登做嗎，我忘記了沒我就忘記我就知知道是啊，這到底是要幹嘛啊，因為你後續你你不是貨在清創嗎，那你後續操有辦法在申那個有利息補貼啊喔對啊，我應該知道說對你應該知道說你申請的清創貸款有利息補貼吧，對我我對啊我本子都有啦，我不知道就什麼那所以所以你所以你你要照時的去登入這個平臺啊，嗯哼對啊這個這個在你所以我等下用電腦我等下用電腦了因為，他那個亂碼太長了我來用電腦登你是然後，如果可以進的話登不是這樣，就確認就ok的就沒事了，是不是對沒有沒有法沒有沒有你不是隻有登入而已，你你好要登入之後啊，對呃你登錄之後，你就一步一步的去操作，反正你的登錄頁面，你去選那個年度資料維護後你是輸入每一年度的營業跟僱用員工人數這樣子就填點就就好了，對一定要填這個前一年的營業額中營業跟員工人數可是等就要打掉要問代書我那個筷支呃，你看你有沒有是林一表啊，是你一表優啊，那你看你要不要就是直下來幫我弄呃沒有啊，你反正你就是要填資料去啊，如果你沒有填你會影響到說你利息補貼的部分對你就可能就沒有辦法再領利息補貼了喔對那先生我我跟你講你填完之後，你可以再哪邊詢問說你有沒有填了有沒有登錄這件事情就沒有確認了對你可以再跟我們確認那對對對啊我先稍微就跟你講怎麼去操作登錄好，反正你就是一定要登錄好，我等一下，那你對好，那你你只用你那這個分機嗎上是幾號啊等一下，我跟你講我分機我沒法你你你直接因為有可能我們同事結到你就說你要找六六一五林小姐呃，對等我一下六六六六一五對林小姐好對你之後，你可以說你，你可以再問我說喔您的登錄的狀況這樣子我稍微跟你講，反正你就是一定要登登入填寫你的營業額跟員工人數你前一年就是去年的就對了啦，對對總營業額啊，去年營業對那先生就是一樣我我沒關係，我先提醒你，你之後，你只要在利息補貼期間你定要五月底之前去輸入這個平臺上的資料好，你還在利即就是你，你明年也要去做，反正你只要都要對你還要還在利息補貼期間都要就是就是怎樣就是還五年短五年燙完就是零五年就對了了就要做五年就對了這個東西登入不果說你五年的話會多一年喔，你會多登一年我等於因為，你對因為你要講你你看你今年就是登入去年的嗎，所以你最後一年就是呃會在登入他上一年的的資料，對反正就是到時候你會多一年好，不然我等一下要電腦趕快看好不好不會了可題好沒關係沒關係，那你你先操作啦反那你之後就是要提醒你之後還是要登入平臺喔這個優關你的利息補貼每年就對了不理他，不行了，就每你沒b補貼了對啊對啊這個就是要了解說啊，你們企業貨代新創貸款之後的狀況而已啦喔，那大也會以對當然也就是影響利息補貼沒關係，你先操作看看那你還在這邊小我這次問我的那你快失就對了啦呃可以啊，你就看你要不要士林一表去去加總的營業還是說你這邊有資料你自己把他加起來總營業了就可以啦我都十快時，在弄的這個我都沒關係沒關係你你你可以再打電問說，你有沒有登錄了，那我再請我們好不來幫你查那再先先對你先去弄好了好ok好謝謝ok好o先。"
			}
		],
		"rtf": 0.18,
		"score": 0
	},
	"service": "recognize",
	"session_id": "VAWtTfI1O9LQTNCd_6hG7ggeXRgEWrPjG_5yQPffhV4oAUK0wa",
	"status": 0
}
2024-01-16 14:38:30.339 [DEBU] {60a6735071c0aa17a00a164104dbc2c2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:VAWtTfI1O9LQTNCd_6hG7ggeXRgEWrPjG_5yQPffhV4oAUK0wa text:先班說有中心您好敝姓您高姓為您喂您好嘿你好，一我一次收到你們這個簡訊是什麼意思，蛤登錄平臺嗎，對呃今年創業及啓動金額金貸融零貸款樓對呀，這個就是你你不是貨拿嘿對然後勒你要登入那個融資平臺去登錄您公司的前一年度的營業額跟員工人數啊先生你等湖我姓修邱先生公司是新給我笑喔誒四二九八三十三任四二九八三四三二三四三二我我五原遠忘記這是什麼東西的就是你上貸款啊喔就是貸款一百萬那個對啊，你旁業機使中金貸款嗎，您公司是繁榮有限公司嗎，嘿對對你是負責人本人嗎，嘿對那我還用現在哪時候貨代清創的貨代啊，我有點忘記了餒抱歉因為中京我我今年去住了兩次，願有點記憶力有點好對ok，所以你就是收到那個通知對一晶對對啊我我一直奇怪啊，這是什麼東西，我一直想不起來啊，你是哪一些銀行申貸呃在的呃臺灣銀行啊分行呢，呃中信分行臺銀的中心，中信信村分行嗎，對對對中京新村好，所以你忘記哪收貨代，反正你就是收到那個平臺就是通知的簡訊，那先生那我就是請你要到平臺去登入這個資料啊，他說五月三十一號點料登錄啊，你現在如果說可以的話你就現在登錄因為我號，你會忘你要我用你叫我用好不好你你就是照那個平臺裡面之後，那手機點就可以了，是不是如果你手機可以用的話喔我否我現在我現在有收來n然後我第一次登入是按第一次還是我對過對第一次登錄對好了，那輸入你的統編等一下然後他會傳驗證碼到你的手機好傳怎嗎到我手機你已經登入過了誒基本在先完你登入葉面抄那你那你就是回到我已經登錄過那個案件誒他現在跳說哼嗯還正要我輸入那個餒桶編跟密碼餒啊密碼你記得嗎，試看看好不好嗯是我的訂話號碼吧密碼服務那前著你你密碼就是你先輸入同編個密碼然後按登錄密碼，輸預算，不然你按靜下方一個案忘記密碼他不是有有一個忘記密碼選項嗎，我我從我重重新編去裡連連結嗯好我現在現在選要寫現我一直登入過了嗎，對啊對我已經的路過然後欸一樣誒，他是要一樣的統編跟密碼勒對呀對啊，你就輸入啊，那你密碼是忘記的意思嗎，對三次三二然後選下面忘記密碼對然後，他請你要輸入喔統編跟手機對然後，你看你可不可以收到那個簡訊密碼與發動是手機，那你看一下簡訊有沒有收到喔有了我自己穿那麼大段的是英文字喔對啊，那你就你先進去然後，你只要去改你你比較好記的密碼應該是你等我一下好不好嗯啊你我現下去，現在你的畫面是到哪裡啊，你現在的畫面等是怎樣他，他他現在我現在要手法半頁然後蘇旁編我想樣查我看能不能貼上我掃發他不不隔貼上先不是密集亂碼誒，那你之前都沒有登做嗎，我忘記了沒我就忘記我就知知道是啊，這到底是要幹嘛啊，因為你後續你你不是貨在清創嗎，那你後續操有辦法在申那個有利息補貼啊喔對啊，我應該知道說對你應該知道說你申請的清創貸款有利息補貼吧，對我我對啊我本子都有啦，我不知道就什麼那所以所以你所以你你要照時的去登入這個平臺啊，嗯哼對啊這個這個在你所以我等下用電腦我等下用電腦了因為，他那個亂碼太長了我來用電腦登你是然後，如果可以進的話登不是這樣，就確認就ok的就沒事了，是不是對沒有沒有法沒有沒有你不是隻有登入而已，你你好要登入之後啊，對呃你登錄之後，你就一步一步的去操作，反正你的登錄頁面，你去選那個年度資料維護後你是輸入每一年度的營業跟僱用員工人數這樣子就填點就就好了，對一定要填這個前一年的營業額中營業跟員工人數可是等就要打掉要問代書我那個筷支呃，你看你有沒有是林一表啊，是你一表優啊，那你看你要不要就是直下來幫我弄呃沒有啊，你反正你就是要填資料去啊，如果你沒有填你會影響到說你利息補貼的部分對你就可能就沒有辦法再領利息補貼了喔對那先生我我跟你講你填完之後，你可以再哪邊詢問說你有沒有填了有沒有登錄這件事情就沒有確認了對你可以再跟我們確認那對對對啊我先稍微就跟你講怎麼去操作登錄好，反正你就是一定要登錄好，我等一下，那你對好，那你你只用你那這個分機嗎上是幾號啊等一下，我跟你講我分機我沒法你你你直接因為有可能我們同事結到你就說你要找六六一五林小姐呃，對等我一下六六六六一五對林小姐好對你之後，你可以說你，你可以再問我說喔您的登錄的狀況這樣子我稍微跟你講，反正你就是一定要登登入填寫你的營業額跟員工人數你前一年就是去年的就對了啦，對對總營業額啊，去年營業對那先生就是一樣我我沒關係，我先提醒你，你之後，你只要在利息補貼期間你定要五月底之前去輸入這個平臺上的資料好，你還在利即就是你，你明年也要去做，反正你只要都要對你還要還在利息補貼期間都要就是就是怎樣就是還五年短五年燙完就是零五年就對了了就要做五年就對了這個東西登入不果說你五年的話會多一年喔，你會多登一年我等於因為，你對因為你要講你你看你今年就是登入去年的嗎，所以你最後一年就是呃會在登入他上一年的的資料，對反正就是到時候你會多一年好，不然我等一下要電腦趕快看好不好不會了可題好沒關係沒關係，那你你先操作啦反那你之後就是要提醒你之後還是要登入平臺喔這個優關你的利息補貼每年就對了不理他，不行了，就每你沒b補貼了對啊對啊這個就是要了解說啊，你們企業貨代新創貸款之後的狀況而已啦喔，那大也會以對當然也就是影響利息補貼沒關係，你先操作看看那你還在這邊小我這次問我的那你快失就對了啦呃可以啊，你就看你要不要士林一表去去加總的營業還是說你這邊有資料你自己把他加起來總營業了就可以啦我都十快時，在弄的這個我都沒關係沒關係你你你可以再打電問說，你有沒有登錄了，那我再請我們好不來幫你查那再先先對你先去弄好了好ok好謝謝ok好o先。]
2024-01-16 15:13:56.288 [DEBU] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=FTbKr9eroQCWsPVX&call-id=pwSPyw2f0oZae9xi&request-id=lJtJJie0GevYqqhA
2024-01-16 15:13:57.467 [DEBU] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 2.38,
		"elapsed": 257,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -1.62,
				"transcript": "十。"
			}
		],
		"rtf": 0.11,
		"score": 0
	},
	"service": "recognize",
	"session_id": "pwSPyw2f0oZae9xi_lJtJJie0GevYqqhA_FTbKr9eroQCWsPVX",
	"status": 0
}
2024-01-16 15:13:57.469 [DEBU] {9083548f79c2aa17a10a16410ada6705} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:pwSPyw2f0oZae9xi_lJtJJie0GevYqqhA_FTbKr9eroQCWsPVX text:十。]
2024-01-16 15:18:15.969 [DEBU] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=QTKXlRKd4ZV7yNXx&call-id=hLn4WcZ6QTuLlB8O&request-id=fiwLsFWpov2VcHhJ
2024-01-16 15:18:17.133 [DEBU] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 2.38,
		"elapsed": 135,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -1.62,
				"transcript": "十。"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "hLn4WcZ6QTuLlB8O_fiwLsFWpov2VcHhJ_QTKXlRKd4ZV7yNXx",
	"status": 0
}
2024-01-16 15:18:17.133 [DEBU] {68237406b6c2aa173fad982746d992e2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:hLn4WcZ6QTuLlB8O_fiwLsFWpov2VcHhJ_QTKXlRKd4ZV7yNXx text:十。]
2024-01-16 15:20:01.394 [DEBU] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?auto-digit-convert=true&sample-rate=16000&do-punctuation=true&user-id=Vhh1efbYdc2AHAz3&call-id=dMQVH54veWxPSCM3&request-id=o7PhHbSiU7VDb39o
2024-01-16 15:20:02.938 [DEBU] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.69,
		"elapsed": 457,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.56,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.1,
		"score": 0
	},
	"service": "recognize",
	"session_id": "dMQVH54veWxPSCM3_o7PhHbSiU7VDb39o_Vhh1efbYdc2AHAz3",
	"status": 0
}
2024-01-16 15:20:02.938 [DEBU] {60122f92cec2aa1740ad9827d96f9cb2} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:dMQVH54veWxPSCM3_o7PhHbSiU7VDb39o_Vhh1efbYdc2AHAz3 text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 15:20:48.907 [DEBU] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=NKrIQOeBDDchVWhH&call-id=BiVRffCnRRBbcWfn&request-id=A5pUM5BK1aDOURZx
2024-01-16 15:20:49.957 [DEBU] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.69,
		"elapsed": 340,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.56,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.07,
		"score": 0
	},
	"service": "recognize",
	"session_id": "BiVRffCnRRBbcWfn_A5pUM5BK1aDOURZx_NKrIQOeBDDchVWhH",
	"status": 0
}
2024-01-16 15:20:49.957 [DEBU] {a0ec03a2d9c2aa1741ad98271e902c24} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:BiVRffCnRRBbcWfn_A5pUM5BK1aDOURZx_NKrIQOeBDDchVWhH text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 15:22:07.283 [DEBU] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=CjB0Q809wDuB6Hdh&call-id=xIwOjvv1gYjls51E&request-id=iU7ZKgsehCCr2O8i
2024-01-16 15:22:08.573 [DEBU] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.71,
		"elapsed": 313,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.85,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.07,
		"score": 0
	},
	"service": "recognize",
	"session_id": "xIwOjvv1gYjls51E_iU7ZKgsehCCr2O8i_CjB0Q809wDuB6Hdh",
	"status": 0
}
2024-01-16 15:22:08.573 [DEBU] {d0f48de1ebc2aa1742ad9827dd9c804e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:xIwOjvv1gYjls51E_iU7ZKgsehCCr2O8i_CjB0Q809wDuB6Hdh text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 15:22:59.486 [DEBU] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?do-punctuation=true&auto-digit-convert=true&sample-rate=16000&user-id=aiLxtpccP0u0axq0&call-id=8kVD6TCQJ6Bt5dXm&request-id=qkOOMr7rOnNLmFpJ
2024-01-16 15:23:00.414 [DEBU] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 3.55,
		"elapsed": 210,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -2.04,
				"transcript": "誒！"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "8kVD6TCQJ6Bt5dXm_qkOOMr7rOnNLmFpJ_aiLxtpccP0u0axq0",
	"status": 0
}
2024-01-16 15:23:00.414 [DEBU] {f0896809f8c2aa1743ad982756221f95} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:8kVD6TCQJ6Bt5dXm_qkOOMr7rOnNLmFpJ_aiLxtpccP0u0axq0 text:誒！]
2024-01-16 15:23:14.298 [DEBU] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?do-punctuation=true&auto-digit-convert=true&sample-rate=16000&user-id=WlxaTEyG47RjU6kl&call-id=YE3K0ZYhLOwgkyzV&request-id=57w4r1187hntNxHb
2024-01-16 15:23:15.640 [DEBU] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 3.55,
		"elapsed": 189,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -2.04,
				"transcript": "誒！"
			}
		],
		"rtf": 0.05,
		"score": 0
	},
	"service": "recognize",
	"session_id": "YE3K0ZYhLOwgkyzV_57w4r1187hntNxHb_WlxaTEyG47RjU6kl",
	"status": 0
}
2024-01-16 15:23:15.641 [DEBU] {38fa4d7cfbc2aa1744ad982737d4614f} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:YE3K0ZYhLOwgkyzV_57w4r1187hntNxHb_WlxaTEyG47RjU6kl text:誒！]
2024-01-16 15:26:53.791 [DEBU] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?do-punctuation=true&auto-digit-convert=true&sample-rate=16000&user-id=DcPCSzxxaLH34Uu1&call-id=tDXpRn2FzreA2Z53&request-id=eabQIhhQZeWtbu20
2024-01-16 15:26:55.490 [DEBU] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.69,
		"elapsed": 294,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.28,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "tDXpRn2FzreA2Z53_eabQIhhQZeWtbu20_DcPCSzxxaLH34Uu1",
	"status": 0
}
2024-01-16 15:26:55.492 [DEBU] {908488952ec3aa1745ad98270a15f809} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:tDXpRn2FzreA2Z53_eabQIhhQZeWtbu20_DcPCSzxxaLH34Uu1 text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 15:27:13.035 [DEBU] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=5wi5C1CVYCiVaOlp&call-id=kTaQjfGqMyWFWOgP&request-id=BIDJrQfbIVkasYLu
2024-01-16 15:27:14.666 [DEBU] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.69,
		"elapsed": 300,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.28,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "kTaQjfGqMyWFWOgP_BIDJrQfbIVkasYLu_5wi5C1CVYCiVaOlp",
	"status": 0
}
2024-01-16 15:27:14.666 [DEBU] {a86a551133c3aa1746ad98274150ce0e} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:kTaQjfGqMyWFWOgP_BIDJrQfbIVkasYLu_5wi5C1CVYCiVaOlp text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 15:27:43.955 [DEBU] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp] emotibot_stt.go:586: Recognize file , params:http://***********:18000/client/dynamic/recognize?sample-rate=16000&do-punctuation=true&auto-digit-convert=true&user-id=1wEmZzPS4bsuhTA7&call-id=JawZDnQBlEYlvtaT&request-id=37ClMmK7t05yI0l6
2024-01-16 15:27:45.163 [DEBU] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1] emotibot_stt.go:650: {
	"message": {
		"type": "result"
	},
	"result": {
		"duration": 4.69,
		"elapsed": 294,
		"final": true,
		"hypotheses": [
			{
				"likelihood": -7.28,
				"transcript": "這是一段七節死語音測試歡迎來到臺北市。"
			}
		],
		"rtf": 0.06,
		"score": 0
	},
	"service": "recognize",
	"session_id": "JawZDnQBlEYlvtaT_37ClMmK7t05yI0l6_1wEmZzPS4bsuhTA7",
	"status": 0
}
2024-01-16 15:27:45.164 [DEBU] {b0b52b453ac3aa1747ad98272944f996} [SonaMesh/internal/logic/emotibot.(*STT).recognizeFileHttp.func1.1] emotibot_stt.go:637: Send to http://localhost:9000 with data  map[code:0 message:success session_id:JawZDnQBlEYlvtaT_37ClMmK7t05yI0l6_1wEmZzPS4bsuhTA7 text:這是一段七節死語音測試歡迎來到臺北市。]
2024-01-16 16:36:17.327 [DEBU] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/logic/emotibot.(*TTS).Synthesis] emotibot_tts.go:123: EmotiBot_TTS .original params : {
	"language": "zh-TW",
	"pitch": 5,
	"samplerate": 8000,
	"speed": 6,
	"text": "这是一段tts的测试文本",
	"type": "wav",
	"user_id": "",
	"volume": 3
}
2024-01-16 16:36:56.745 [DEBU] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/logic/emotibot.(*TTS).Synthesis] emotibot_tts.go:123: EmotiBot_TTS .original params : {
	"language": "zh-TW",
	"pitch": 5,
	"samplerate": 8000,
	"speed": 6,
	"text": "这是一段tts的测试文本",
	"type": "mp3",
	"user_id": "",
	"volume": 3
}
2024-01-16 16:37:21.049 [DEBU] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/logic/emotibot.(*TTS).Synthesis] emotibot_tts.go:123: EmotiBot_TTS .original params : {
	"language": "zh-TW",
	"pitch": 5,
	"samplerate": 8000,
	"speed": 6,
	"text": "这是一段tts的测试文本",
	"type": "pcm",
	"user_id": "",
	"volume": 3
}
