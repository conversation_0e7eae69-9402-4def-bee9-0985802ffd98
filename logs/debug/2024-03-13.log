2024-03-13 11:04:56.391 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:04:56.391 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.392 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.397 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.397 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.397 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.398 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.398 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.399 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.399 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.399 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.400 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.400 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.400 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.401 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.401 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.401 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.402 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.403 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.403 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.403 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.403 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.404 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.404 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.404 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.405 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.405 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.405 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.405 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.405 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.406 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.406 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.406 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.407 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.407 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.407 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.408 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.408 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.408 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.409 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.409 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.410 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.410 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.410 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.411 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.411 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.411 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.412 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.413 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.413 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.413 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.413 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.414 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.414 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.414 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.415 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.415 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.415 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.416 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.416 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.416 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.417 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.417 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.418 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.418 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.418 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.424 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.424 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.424 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.425 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.425 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.425 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.426 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.426 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.426 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.426 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.426 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.427 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.427 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.427 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.428 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.428 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.428 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.428 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.429 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.429 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.429 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.429 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.430 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.430 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.430 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.431 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.431 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.431 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.431 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.431 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.432 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.432 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.432 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.438 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.439 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.439 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.440 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.441 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.441 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.441 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.442 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.442 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.442 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.442 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.442 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.443 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.444 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.444 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.444 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.445 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.445 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.445 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.445 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.445 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.446 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.446 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.446 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.446 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.446 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.447 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.447 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.447 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.448 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.448 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.448 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.448 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.448 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.449 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.449 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.449 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.449 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.450 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.450 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.450 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.450 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.450 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.455 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.455 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.456 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.456 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.456 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.457 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.458 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.458 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.458 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.459 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.459 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.459 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.459 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.459 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.460 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.460 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.460 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.460 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.460 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.461 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.461 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.461 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.462 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.462 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.462 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:56.462 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:04:59.588 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:05:30.379 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:07:22.889 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:07:22.890 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:07:22.892 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.892 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.892 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.892 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.893 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.893 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.893 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.894 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.895 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.895 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.896 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.897 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.897 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.897 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.898 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:22.899 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:07:25.846 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:07:55.888 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:10:18.030 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:10:18.030 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:10:18.031 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:18.533 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:19.034 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:19.536 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:20.036 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:20.537 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:21.038 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:21.542 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:22.043 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:22.544 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:23.044 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:23.545 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:24.048 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:24.549 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:25.049 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:25.550 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:26.051 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:26.552 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:27.054 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:27.555 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:28.061 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:28.560 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:29.064 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:29.562 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:30.062 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:30.563 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:31.065 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:31.566 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:32.067 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:32.569 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:10:32.877 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:10:33.069 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:03.042 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:11:47.654 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:11:47.654 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:11:47.655 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:48.156 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:48.656 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:49.160 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:49.661 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:50.162 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:50.663 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:51.165 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:51.666 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:52.167 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:52.669 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:53.171 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:53.679 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:54.179 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:54.681 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:55.181 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:55.658 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:152: Trigger on finished function ... 
2024-03-13 11:11:55.658 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:136: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:11:55.658 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:157: Stop channel is open ,trigger action stop ack ...
2024-03-13 11:11:55.682 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:56.183 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:56.684 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:57.184 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:57.686 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:58.187 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:58.688 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:59.189 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:11:59.689 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:00.191 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:00.692 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:01.192 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:01.693 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:02.194 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:02.695 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:30.270 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:12:30.270 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:12:30.271 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:30.772 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:31.273 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:31.776 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:32.276 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:32.779 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:33.279 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:33.784 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:34.282 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:34.784 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:35.286 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:35.787 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:36.287 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:36.791 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:37.293 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:37.794 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:38.296 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:38.797 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:39.298 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:39.799 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:40.272 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:152: Trigger on finished function ... 
2024-03-13 11:12:40.272 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:136: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:12:40.272 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:157: Stop channel is open ,trigger action stop ack ...
2024-03-13 11:12:40.300 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:40.801 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:41.302 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:41.803 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:42.304 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:42.805 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:43.307 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:43.811 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:44.311 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:44.812 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:45.313 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:55.549 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:12:55.549 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:12:55.550 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:56.051 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:56.551 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:57.053 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:57.554 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:58.054 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:58.556 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:59.057 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:12:59.557 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:00.059 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:00.560 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:01.062 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:01.563 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:02.064 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:02.565 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:03.066 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:03.567 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:04.068 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:04.569 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:05.074 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:05.576 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:06.077 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:06.580 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:07.081 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:07.582 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:08.086 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:08.588 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:09.089 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:09.551 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:152: Trigger on finished function ... 
2024-03-13 11:13:09.551 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:136: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:13:09.551 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:157: Stop channel is open ,trigger action stop ack ...
2024-03-13 11:13:09.589 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:10.091 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:10.594 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:26.387 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:13:26.387 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:13:26.389 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:26.889 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:27.393 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:27.892 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:28.393 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:28.894 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:29.395 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:29.896 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:30.398 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:30.898 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:31.399 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:31.901 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:32.403 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:32.904 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:33.407 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:33.912 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:34.414 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:34.916 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:35.416 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:35.918 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:36.419 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:36.921 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:37.428 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:37.925 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:38.426 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:38.927 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:39.428 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:39.930 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:40.431 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:40.932 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:13:41.233 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:13:41.433 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:14:11.387 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:15:52.703 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:15:52.704 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:15:52.705 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:53.005 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:53.306 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:53.610 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:53.908 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:54.209 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:54.526 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:54.826 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:55.127 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:55.429 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:55.730 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:56.031 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:56.332 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:56.632 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:56.933 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:57.234 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:57.536 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:57.840 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:58.141 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:58.442 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:58.744 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:59.045 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:59.346 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:59.649 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:15:59.951 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:00.252 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:00.553 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:00.705 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:152: Trigger on finished function ... 
2024-03-13 11:16:00.706 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:136: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 11:16:00.706 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:157: Stop channel is open ,trigger action stop ack ...
2024-03-13 11:16:00.853 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:01.154 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:01.455 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:01.756 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:18.282 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:16:18.282 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:16:18.283 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:18.583 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:18.886 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:19.187 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:19.487 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:19.790 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:20.093 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:20.394 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:20.694 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:20.996 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:21.298 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:21.598 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:21.898 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:22.198 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:22.499 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:22.800 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:23.101 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:23.402 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:23.703 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:24.004 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:24.304 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:24.605 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:24.905 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:25.207 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:25.508 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:25.810 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:26.114 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:26.413 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:26.713 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:27.014 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:27.294 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:16:27.315 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:16:58.276 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:17:05.750 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:17:05.751 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:17:05.751 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:05.953 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:06.153 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:06.355 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:06.555 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:06.755 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:06.956 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:07.156 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:07.358 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:07.559 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:07.759 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:07.960 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:08.161 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:08.362 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:08.562 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:08.763 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:08.964 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:09.165 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:09.365 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:09.566 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:09.767 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:09.967 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:10.168 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:10.369 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:10.570 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:10.771 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:10.972 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:11.173 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:11.375 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:11.576 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:11.777 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:17:12.080 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:17:42.751 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:146: Stop channel is already closed ... 
2024-03-13 11:18:02.689 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/stt.(*sSTT).Stop] stt.go:313: Stop action ack timeout
2024-03-13 11:19:48.554 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:132: Start channel is open then trigger it ...  
2024-03-13 11:19:48.554 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:224: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 11:19:48.555 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:48.757 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:48.958 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:49.159 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:49.360 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:49.561 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:49.762 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:49.963 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:50.164 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:50.364 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:50.565 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:50.766 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:50.968 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:51.169 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:51.369 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:51.570 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:51.773 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:51.973 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:52.176 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:52.378 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:52.578 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:52.780 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:52.981 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:53.182 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:53.383 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:53.585 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:53.785 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:53.987 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:54.188 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:54.388 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:54.590 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 11:19:54.674 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 11:20:25.549 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:142: Stop channel is open then trigger it ...  
2024-03-13 15:21:28.143 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:214: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:21:28.145 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:28.345 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:28.546 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:28.747 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:28.948 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:29.149 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:29.350 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:29.551 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:29.752 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:29.954 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:30.154 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:30.355 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:30.556 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:30.757 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:30.958 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:31.160 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:31.361 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:31.562 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:31.763 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:31.965 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:32.166 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:32.368 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:32.569 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:32.771 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:32.973 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:33.174 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:33.375 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:33.576 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:33.777 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:33.978 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:34.180 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:21:34.285 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:23:35.526 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:214: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:23:35.527 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:35.729 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:35.929 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:36.131 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:36.332 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:36.533 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:36.734 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:36.935 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:37.135 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:37.337 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:37.538 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:37.738 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:37.939 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:38.140 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:38.341 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:38.542 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:38.743 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:38.944 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:39.145 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:39.346 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:39.547 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:39.748 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:39.949 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:40.149 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:40.351 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:40.552 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:40.754 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:40.955 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:41.155 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:41.356 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:41.557 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:23:41.650 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:27:10.230 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:214: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:27:10.231 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:10.432 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:10.633 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:10.835 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:11.036 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:11.237 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:11.438 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:11.638 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:11.838 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:12.039 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:12.241 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:12.442 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:12.643 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:12.852 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:13.049 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:13.250 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:13.451 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:13.652 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:13.853 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:14.054 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:14.256 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:14.457 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:14.658 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:14.860 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:15.060 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:15.262 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:15.463 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:15.663 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:15.865 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:16.065 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:16.266 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:27:16.344 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:126: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這是一段使用Microsoft Azure的TDs合成的**。"
	},
	"status": 0
}
2024-03-13 15:28:45.028 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:214: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-03-13 15:28:45.029 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:45.230 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:45.431 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:45.632 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:45.833 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:46.034 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:46.235 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:46.436 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:46.636 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:46.837 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:47.038 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:47.239 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:47.440 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:47.643 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:47.844 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:48.045 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:48.245 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:48.446 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:48.648 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:48.849 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:49.050 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:49.250 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:49.451 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:49.652 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:49.853 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:50.054 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:50.256 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:50.464 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:50.665 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:50.867 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:51.067 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:96: Received binary message ...
2024-03-13 15:28:53.030 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:144: Trigger on finished function ... 
2024-03-13 15:28:53.030 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:136: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-03-13 15:28:53.030 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:148: Positive to stop...
2024-03-13 15:28:53.032 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor] websocket_man.go:84: websocket: close 1006 (abnormal closure): unexpected EOF
