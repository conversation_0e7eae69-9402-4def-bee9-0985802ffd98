2024-03-13 11:04:56.390 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=d9d8029b930c4284b08f0d18fe352c84
2024-03-13 11:04:56.391 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:04:59.587 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=d9d8029b930c4284b08f0d18fe352c84 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:04:59.588 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:04:59.745 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=d9d8029b930c4284b08f0d18fe352c84
2024-03-13 11:04:59.746 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:05:30.379 [DEBU] {58bf5773f733bc174973af2f20d373a5} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:07:22.889 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=e20926e3043743d7ae74275fa16f0304
2024-03-13 11:07:22.890 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:07:25.845 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=e20926e3043743d7ae74275fa16f0304 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:07:25.846 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:07:25.992 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=e20926e3043743d7ae74275fa16f0304
2024-03-13 11:07:25.993 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:07:55.888 [DEBU] {50b2f68f1934bc174a73af2f09b83a16} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:10:18.030 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=c5f2c50e95b24bfe88f11523a15f3e2a
2024-03-13 11:10:18.031 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:10:32.874 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=c5f2c50e95b24bfe88f11523a15f3e2a Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:10:32.877 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:10:33.037 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=c5f2c50e95b24bfe88f11523a15f3e2a
2024-03-13 11:10:33.037 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:11:03.041 [DEBU] {d0f92a574234bc174b73af2f7f153fba} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:11:47.654 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=f43af59fbce54f3da1aab29920916d40
2024-03-13 11:11:47.654 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:11:55.657 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:250: Identify short command timeout
2024-03-13 11:12:02.482 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=f43af59fbce54f3da1aab29920916d40 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:12:02.840 [DEBU] {e0f427355734bc174c73af2f2fed840a} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=f43af59fbce54f3da1aab29920916d40
2024-03-13 11:12:30.270 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=e986618438cf4a6cbad6062408ca9ed7
2024-03-13 11:12:30.270 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:12:40.271 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:250: Identify short command timeout
2024-03-13 11:12:45.095 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=e986618438cf4a6cbad6062408ca9ed7 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:12:45.464 [DEBU] {a0163c216134bc174d73af2f87cb66b9} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=e986618438cf4a6cbad6062408ca9ed7
2024-03-13 11:12:55.549 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=8aac1cf232d745ccb1aad2aa73f3a790
2024-03-13 11:12:55.549 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:13:09.550 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:250: Identify short command timeout
2024-03-13 11:13:10.385 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=8aac1cf232d745ccb1aad2aa73f3a790 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:13:10.755 [DEBU] {f02706046734bc174e73af2ff5c8357b} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=8aac1cf232d745ccb1aad2aa73f3a790
2024-03-13 11:13:26.387 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=663d724a9f50453c85d286516740b3d1
2024-03-13 11:13:26.388 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:13:41.232 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=663d724a9f50453c85d286516740b3d1 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:13:41.233 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:13:41.380 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=663d724a9f50453c85d286516740b3d1
2024-03-13 11:13:41.380 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:14:11.386 [DEBU] {70401a326e34bc174f73af2fa40a641b} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:15:52.703 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=ae677cb0c6144c618c39a90c0943fa07
2024-03-13 11:15:52.704 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:16:00.705 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:250: Identify short command timeout
2024-03-13 11:16:01.734 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=ae677cb0c6144c618c39a90c0943fa07 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:16:01.907 [DEBU] {10d435439034bc175073af2fa63f7316} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=ae677cb0c6144c618c39a90c0943fa07
2024-03-13 11:16:18.282 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=a2dc94813821412da557aeffb185fbf6
2024-03-13 11:16:18.282 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:16:27.293 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=a2dc94813821412da557aeffb185fbf6 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:16:27.294 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:16:27.437 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=a2dc94813821412da557aeffb185fbf6
2024-03-13 11:16:27.437 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:16:58.276 [DEBU] {30ead2379634bc175173af2f72b1ae1c} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:17:05.750 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=5484172eefec4a50a44f5ccc7d3e7cf5
2024-03-13 11:17:05.751 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:17:12.078 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=5484172eefec4a50a44f5ccc7d3e7cf5 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:17:12.079 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:17:12.277 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=5484172eefec4a50a44f5ccc7d3e7cf5
2024-03-13 11:17:12.277 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:17:42.750 [DEBU] {a0042b45a134bc175273af2f94bf9110} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 11:19:48.554 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=431cc347cb06448f9453316a943c183e
2024-03-13 11:19:48.555 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 11:19:54.672 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=431cc347cb06448f9453316a943c183e Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 11:19:54.674 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 11:19:54.819 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=431cc347cb06448f9453316a943c183e
2024-03-13 11:19:54.819 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 11:20:25.548 [DEBU] {3864ad2cc734bc1749d3d1759f51f704} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 15:21:28.142 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=ac1a905042914fb597b1ef4a05306896
2024-03-13 15:21:28.143 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 15:21:34.285 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=ac1a905042914fb597b1ef4a05306896 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 15:21:34.285 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 15:21:34.448 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=ac1a905042914fb597b1ef4a05306896
2024-03-13 15:21:34.448 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 15:22:05.134 [DEBU] {784aa21ff741bc17869ea76d8469cbc1} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 15:23:35.526 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=dc6c24c8985147af84838d9c79efbd0b
2024-03-13 15:23:35.526 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 15:23:41.646 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=dc6c24c8985147af84838d9c79efbd0b Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 15:23:41.647 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 15:23:41.795 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=dc6c24c8985147af84838d9c79efbd0b
2024-03-13 15:23:41.796 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 15:24:12.519 [DEBU] {c00c1dc81442bc171fce6166ece5e6d5} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 15:27:10.228 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=31cdf815be784df78bc0c7b1bc53f5b6
2024-03-13 15:27:10.230 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 15:27:16.344 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=31cdf815be784df78bc0c7b1bc53f5b6 Text=這是一段使用Microsoft Azure的TDs合成的**。 
2024-03-13 15:27:16.344 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:258: Recognized Results Of Short Instructions:這是一段使用Microsoft Azure的TDs合成的**。
2024-03-13 15:27:16.485 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=31cdf815be784df78bc0c7b1bc53f5b6
2024-03-13 15:27:16.485 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:352: Short command channel is closed  
2024-03-13 15:27:47.226 [DEBU] {c0495bc54642bc17a39ac14f3a39e92c} [SonaMesh/internal/logic/azure.(*STT).Stop] azure_stt.go:414: Not start recognition  or recognition is over ... 
2024-03-13 15:28:45.028 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:195: Session start : ID=ad53d9da080e481cbf2c6d0d791889b2
2024-03-13 15:28:45.028 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:245: Wait recognize result ... 
2024-03-13 15:28:53.029 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:250: Identify short command timeout
2024-03-13 15:28:53.371 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:364: Recognized : ID=ad53d9da080e481cbf2c6d0d791889b2 Text= 
2024-03-13 15:28:53.372 [DEBU] {28923bd85c42bc17a49ac14f4a0ed9f3} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:318: Session stop : ID=ad53d9da080e481cbf2c6d0d791889b2
