2024-05-28 15:36:41.953 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=5f025a546954466db1d588f5e0bdb677
2024-05-28 15:36:46.927 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=你好。 
2024-05-28 15:36:54.808 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。 
2024-05-28 15:36:56.569 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=那。 
2024-05-28 15:37:04.916 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=你們那個日期啊是那麼小，我以為是到12月31號就是到12月30號啊。 
2024-05-28 15:37:09.873 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=對，因為我們往年每一年的勸他都是到12月30號。 
2024-05-28 15:37:13.945 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=所以卷的使用期限確實是到12月30。 
2024-05-28 15:37:19.961 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=那可是差一天，為什麼你們門市又不肯讓我？ 
2024-05-28 15:37:21.537 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=折抵啊。 
2024-05-28 15:37:26.319 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=哎，買很多杯買十幾杯。 
2024-05-28 15:37:33.908 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=小姐，不好意思，因為這不是插插幾天，而是因為他就是過期門市，就沒辦法收卷了。 
2024-05-28 15:37:45.413 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=那這裡有沒有什麼方式可以？這個不我們啦，因為這個很多張欸，我有很多張，哎，不是一張而已，哎。 
2024-05-28 15:37:52.276 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=我了解，但是因為他是一整年度的兌換，所以這個券的部分他沒有辦法再補，惟。 
2024-05-28 15:38:23.000 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout] azure_stt.go:177: Receive timeout ... 
2024-05-28 15:38:23.376 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=5f025a546954466db1d588f5e0bdb677 Text=那有沒有什麼方式可以彌補我們的這種損失啊？因為那個很。 
2024-05-28 15:38:23.385 [DEBU] {78001806e796d317f7de676bc42bd299} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=5f025a546954466db1d588f5e0bdb677
2024-05-28 15:44:57.919 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=2d9dc9be7ef64e4a8ab026654ee43fef
2024-05-28 15:45:02.890 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2d9dc9be7ef64e4a8ab026654ee43fef Text=你好。 
2024-05-28 15:45:10.775 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2d9dc9be7ef64e4a8ab026654ee43fef Text=小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。 
2024-05-28 15:45:12.545 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2d9dc9be7ef64e4a8ab026654ee43fef Text=那。 
2024-05-28 15:45:42.889 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout] azure_stt.go:177: Receive timeout ... 
2024-05-28 15:45:43.102 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2d9dc9be7ef64e4a8ab026654ee43fef Text=你們那個日期啊。 
2024-05-28 15:45:43.107 [DEBU] {b8cc13805a97d3173bf0c31ca662be6f} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=2d9dc9be7ef64e4a8ab026654ee43fef
2024-05-28 15:46:45.608 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=16b4d235cefa4aa6a22997653655f1e3
2024-05-28 15:46:50.590 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=你好。 
2024-05-28 15:46:58.477 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。 
2024-05-28 15:47:00.234 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=那。 
2024-05-28 15:47:08.596 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=你們那個日期啊是那麼小，我以為是到12月31號就是到12月30號啊。 
2024-05-28 15:47:13.545 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=對，因為我們往年每一年的勸他都是到12月30號。 
2024-05-28 15:47:43.590 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout] azure_stt.go:177: Receive timeout ... 
2024-05-28 15:47:43.754 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=16b4d235cefa4aa6a22997653655f1e3 Text=所以卷的使用期限確實是到12月30。 
2024-05-28 15:47:43.758 [DEBU] {e02d4f937397d3173cf0c31c098f224a} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=16b4d235cefa4aa6a22997653655f1e3
2024-05-28 15:48:18.567 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=2538c6e87d5c4c43a105929277de1617
2024-05-28 15:48:18.568 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:256: Wait recognize result ... 
2024-05-28 15:48:23.529 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2538c6e87d5c4c43a105929277de1617 Text=你好。 
2024-05-28 15:48:23.530 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).waitShortCommandRecog] azure_stt.go:269: Recognized Results Of Short Instructions:你好。
2024-05-28 15:48:23.646 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=2538c6e87d5c4c43a105929277de1617
2024-05-28 15:48:23.646 [DEBU] {984128388997d3173df0c31cc58f32d1} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:375: Recognition has ended stop waiting
2024-05-28 15:50:53.012 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=c4fbf64395e54956b90315005568959a
2024-05-28 15:50:57.977 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=c4fbf64395e54956b90315005568959a Text=你好。 
2024-05-28 15:51:05.858 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=c4fbf64395e54956b90315005568959a Text=小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。 
2024-05-28 15:51:35.951 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout] azure_stt.go:177: Receive timeout ... 
2024-05-28 15:51:36.085 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=c4fbf64395e54956b90315005568959a Text=那。 
2024-05-28 15:51:36.121 [DEBU] {a08dba2dad97d317f1a3ad7083d80c2d} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=c4fbf64395e54956b90315005568959a
2024-05-28 15:53:03.160 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).sessionStart] azure_stt.go:206: Session start : ID=2fb85c5c1c094a6084646c94b572113e
2024-05-28 15:53:08.127 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2fb85c5c1c094a6084646c94b572113e Text=你好。 
2024-05-28 15:53:15.987 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2fb85c5c1c094a6084646c94b572113e Text=小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡券嘛是。 
2024-05-28 15:53:17.763 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2fb85c5c1c094a6084646c94b572113e Text=那。 
2024-05-28 15:53:48.080 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).checkRecvTimeout] azure_stt.go:177: Receive timeout ... 
2024-05-28 15:53:48.330 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).recognized] azure_stt.go:391: Recognized : ID=2fb85c5c1c094a6084646c94b572113e Text=你們那個。 
2024-05-28 15:53:48.331 [DEBU] {c8501d7bcb97d31764f0775ccbaa36c5} [SonaMesh/internal/logic/azure.(*STT).sessionStopped] azure_stt.go:335: Session stop : ID=2fb85c5c1c094a6084646c94b572113e
