2024-01-16 16:13:00.703 [DEBU] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:482: session started : ID = 13665cc05f364c49a85cfdf4c19e487c
2024-01-16 16:13:03.051 [DEBU] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:494: recognized : text [這是一段TDs的語音測試，歡迎來到臺北市。] , duration:[3.92s], offset:[80ms]
2024-01-16 16:13:03.069 [DEBU] {104e49cdb2c5aa17815c761fae34283c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:487: session stopped : ID = 13665cc05f364c49a85cfdf4c19e487c
2024-01-16 16:22:56.271 [DEBU] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:482: session started : ID = 6dd95b88a9e34c19a338f60d7050fa31
2024-01-16 16:22:58.466 [DEBU] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:494: recognized : text [這是一段TDs的語音測試，歡迎來到臺北市。] , duration:[3.92s], offset:[80ms]
2024-01-16 16:22:58.473 [DEBU] {a0133a793dc6aa1771bf1679ed360a5d} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:487: session stopped : ID = 6dd95b88a9e34c19a338f60d7050fa31
2024-01-16 16:23:58.532 [DEBU] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:482: session started : ID = 0b3c95fecf1c44fc95a3b97b3ecf9152
2024-01-16 16:24:00.826 [DEBU] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:494: recognized : text [這是一段TDs的語音測試，歡迎來到臺北市。] , duration:[3.93s], offset:[70ms]
2024-01-16 16:24:00.838 [DEBU] {d87d72f94bc6aa1772bf16791ffa00a9} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:487: session stopped : ID = 0b3c95fecf1c44fc95a3b97b3ecf9152
2024-01-16 16:49:13.194 [DEBU] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705394953194795.wav
2024-01-16 16:49:13.210 [DEBU] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="1" volume="1"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

2024-01-16 16:49:31.494 [DEBU] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705394971494419.wav
2024-01-16 16:49:31.496 [DEBU] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="1" volume="100"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

2024-01-16 16:49:45.573 [DEBU] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705394985573276.wav
2024-01-16 16:49:45.574 [DEBU] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="1.1" volume="100"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

2024-01-16 16:49:56.619 [DEBU] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705394996619460.wav
2024-01-16 16:49:56.623 [DEBU] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="1.1" volume="100"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

2024-01-16 16:50:29.152 [DEBU] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705395029151997.mp3
2024-01-16 16:50:29.154 [DEBU] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="2" volume="100"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

2024-01-16 16:51:01.560 [DEBU] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:134: The tts output file:./voc/2024-01-16/1705395061560085.wav
2024-01-16 16:51:01.561 [DEBU] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/azure.(*TTS).Synthesis] azure_tts.go:168: SSML: <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-TW">
    <voice name="zh-TW-HsiaoChenNeural" >
     <prosody rate="2" volume="100"  pitch="default">
          这是一段tts的测试文本 
      </prosody>
    </voice>
</speak>

