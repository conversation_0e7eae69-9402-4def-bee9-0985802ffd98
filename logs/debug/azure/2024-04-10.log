2024-04-10 11:21:18.618 [DEBU] {98ae72081ccdc417f8a0b432b47afa82} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:523: session started : ID = c05a3189e95346ffacb25302216a15c4
2024-04-10 11:21:21.728 [DEBU] {98ae72081ccdc417f8a0b432b47afa82} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [這是一段使用Microsoft Azure的TDs合成的**。] , duration:[5.07s], offset:[120ms]
2024-04-10 11:21:21.737 [DEBU] {98ae72081ccdc417f8a0b432b47afa82} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:532: session stopped : ID = c05a3189e95346ffacb25302216a15c4
2024-04-10 11:21:28.903 [DEBU] {b0db9d6d1ecdc417f9a0b43277b0b4a3} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:523: session started : ID = 23cbca4ace8347c89105f4479b45c667
2024-04-10 11:21:31.439 [DEBU] {b0db9d6d1ecdc417f9a0b43277b0b4a3} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [這是一段使用Microsoft Azure的TDs合成的**。] , duration:[5.07s], offset:[120ms]
2024-04-10 11:21:31.440 [DEBU] {b0db9d6d1ecdc417f9a0b43277b0b4a3} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:532: session stopped : ID = 23cbca4ace8347c89105f4479b45c667
2024-04-10 11:21:45.132 [DEBU] {404ddf3522cdc417faa0b432c411d87c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:523: session started : ID = 4cfb01a5b784435082dec7ff6741123d
2024-04-10 11:21:47.823 [DEBU] {404ddf3522cdc417faa0b432c411d87c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [這是一段使用Microsoft Azure的TDs合成的**。] , duration:[5.07s], offset:[120ms]
2024-04-10 11:21:47.831 [DEBU] {404ddf3522cdc417faa0b432c411d87c} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:532: session stopped : ID = 4cfb01a5b784435082dec7ff6741123d
2024-04-10 11:22:13.587 [DEBU] {40eea6d528cdc417fba0b43251ccd6a2} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:523: session started : ID = bc4e4a608cec44869474682d0dc5677e
2024-04-10 11:22:16.150 [DEBU] {40eea6d528cdc417fba0b43251ccd6a2} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [這是一段使用Microsoft Azure的TDs合成的**。] , duration:[5.07s], offset:[120ms]
2024-04-10 11:22:16.151 [DEBU] {40eea6d528cdc417fba0b43251ccd6a2} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:532: session stopped : ID = bc4e4a608cec44869474682d0dc5677e
2024-04-10 11:22:27.215 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.2] azure_stt.go:523: session started : ID = 0c6d0723e21c4b2c8d49ef7448a168a1
2024-04-10 11:22:29.181 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [馬沙曼福中心，您好，比起您高興為您服務。] , duration:[3.25s], offset:[600ms]
2024-04-10 11:22:31.188 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喂，你好，那個如果是我那個創業貸款是問問這邊嘛啊？對這邊可以幫您做說明。] , duration:[6.91s], offset:[4.6s]
2024-04-10 11:22:38.763 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [哦，那個我想問說她，我看網路上可以申請到3月31日，後面就不能申請了，嗎喔跟小姐請教一下怎麼稱呼呢？我姓丘好丘小姐，您好，您是從哪個縣市來電的呢？] , duration:[14.66s], offset:[12.36s]
2024-04-10 11:22:53.952 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [因為臺中臺中好，我跟那個邱小姐稍微解釋一下，就是說青創貸款呢？本身這個方案是沒有期限的限制啦，那只是說，因為從去年開始輕鬆貸款，如果你有貨代成功貸款近100萬以下的，有那個最長5年的利息補貼嗎？那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件，才有這個資格去申請這個利息的。] , duration:[30.05s], offset:[27.81s]
2024-04-10 11:22:54.168 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [補貼。] , duration:[520ms], offset:[57.91s]
2024-04-10 11:22:58.743 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，你超過3月底才申請青創貸款的話，差別就是沒有那個100萬5年的利息補貼。] , duration:[8.2s], offset:[59.71s]
2024-04-10 11:23:00.955 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，那如果沒有那個補貼的話是。] , duration:[2.86s], offset:[1m9.39s]
2024-04-10 11:23:16.171 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [就是申請下來，第一年開始就是要還還款這樣嗎？還是因為本來本來信用貸款本來就是一開始，你要先本金加利息做繳款啊？對，因為我們利息補貼的方式還是會還他的方式，還是說你第一個月或帶下來，你還是先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼的那個費用再撥到你的賬戶裡面對。] , duration:[30.04s], offset:[1m13.03s]
2024-04-10 11:23:17.003 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [他才會再撥到你的賬戶裡面。] , duration:[1.91s], offset:[1m43.14s]
2024-04-10 11:23:22.233 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，就是有他在那個其實是一樣的對，只是差在就是說你後面可能就領不到那個利息的補貼的費用。] , duration:[8.56s], offset:[1m46.25s]
2024-04-10 11:23:26.490 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [了解，那我想問一下，說，誒，他申請這個是一定要先登記那個公司嘛，對，沒錯，是的。] , duration:[7.95s], offset:[1m56.22s]
2024-04-10 11:23:28.184 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [那你如果我們是。] , duration:[2.52s], offset:[2m5.21s]
2024-04-10 11:23:30.762 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [比如說是要加盟一間飲料店。] , duration:[2.71s], offset:[2m9.45s]
2024-04-10 11:23:32.321 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [那他的就是這一間公司的負責人，就是。] , duration:[2.72s], offset:[2m13.47s]
2024-04-10 11:23:35.691 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [呃，如果假設有3個人都要申請貸款，就可以前同一公司嗎？還是。] , duration:[4.25s], offset:[2m18.77s]
2024-04-10 11:23:43.881 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [沒有誒，你在辦公室登記的時候啊，只能當一個人當負責人吶。現在青創貸款也只有負責人能做貸款的申請，所以你有其他的股東啊，或者是出資人是不能申請青創貸款的。] , duration:[15.86s], offset:[2m23.65s]
2024-04-10 11:23:47.646 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [就只有一個人可以對對，如果超過100萬的話是。] , duration:[6.07s], offset:[2m41.24s]
2024-04-10 11:23:49.104 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [要大概什麼事？] , duration:[1.03s], offset:[2m49s]
2024-04-10 11:24:04.612 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [沒有他的頭條件都是一樣的啊，他都是要符合年齡上課以及事業體成立未超過5年，那至於貸款金額，你就是以你資金需求的部分去做填寫嗎？你會看到我們輕鬆貸款要點，上面有分3個貸款用途喔，準備金跟開辦費用最高200萬，週轉性支出最高四400萬。主要銀行端，她在審查的時候會去評估這個負責人的債信。] , duration:[29.85s], offset:[2m51.52s]
2024-04-10 11:24:07.914 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [以及還款能力哦，還有你實際貸款的用途是不是真的有需要這麼多？] , duration:[6.69s], offset:[3m21.76s]
2024-04-10 11:24:09.427 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好銀行會去做一個全盤的考量。] , duration:[2.45s], offset:[3m29.06s]
2024-04-10 11:24:12.493 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，最主要他們在考量的就是在還款來源的部分。] , duration:[4.91s], offset:[3m32.45s]
2024-04-10 11:24:15.314 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [就是負責人，臺南跟你說，如果是。] , duration:[4.16s], offset:[3m39.42s]
2024-04-10 11:24:16.744 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [第一個，那個200萬是神。] , duration:[1.58s], offset:[3m44.87s]
2024-04-10 11:24:18.031 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [準備金跟開辦費用？] , duration:[1.67s], offset:[3m47.07s]
2024-04-10 11:24:26.276 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，就剛開店的資金嗎？對，就是，但是你還是要有公司行號設立好，我們準備進攻開辦費用試紙設立後的8個月內，你所需的費用，比如說裝潢費啦喔，或者是一些水電。] , duration:[15.35s], offset:[3m50.22s]
2024-04-10 11:24:30.170 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [是購買一些設備，只要是8個月以內的話，都可以先從準備金的費用心提出申請。] , duration:[6.83s], offset:[4m6.41s]
2024-04-10 11:24:32.833 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [啊，那個週轉金的話，為什麼週轉金金額可以比較高？] , duration:[4.49s], offset:[4m14.38s]
2024-04-10 11:24:37.033 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [沒有，昨晚已經因為就是貸款用途，因為有些人他可能成立超過8個月了，那他周轉的需求可能比較高。] , duration:[7.35s], offset:[4m20s]
2024-04-10 11:24:42.776 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [對，但是還是要看實際的經歷，貸款的需求是多少，他也不是說400萬就一次貸400萬給你，他是要看你實際的貸款的需求。] , duration:[10.65s], offset:[4m28.24s]
2024-04-10 11:24:53.404 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，因為我想說如果我穿那個飲料店加盟就要200多萬，我想如果開放費200萬加好像不高？嗯哼，因為如果你全部都已經都用貸款，相對的，你這樣子的風險也是比較高的，因為我們現在創業貸款是屬於你要創業的滯後，那後續不足，再用這個創業貸款來做補充。] , duration:[20.42s], offset:[4m39.98s]
2024-04-10 11:24:56.309 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [And對啊，所以才會說一定要有公司或行號的設立。] , duration:[4.8s], offset:[5m1.94s]
2024-04-10 11:24:59.430 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [那如果知道其他龍的話就是請加盟那邊協助辦理。] , duration:[4.77s], offset:[5m8.18s]
2024-04-10 11:25:02.355 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [不是不，你不是用加盟商的統編喔，是你自己的統編喔。] , duration:[4.78s], offset:[5m14.23s]
2024-04-10 11:25:03.712 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [對對就是。] , duration:[1.63s], offset:[5m20.16s]
2024-04-10 11:25:14.299 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [鴨毛要自己再創一個公司，然後用那個再下去，對呀，就比如說外面很多50啦，那每一家50他都有屬於自己的工商登記，對你就是要有屬於自己的工商登記之後才能做到工商登記，然後再去把它才能辦。] , duration:[20.02s], offset:[5m22.68s]
2024-04-10 11:25:21.209 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [貸款對，就是我剛剛最一開始講的那個3個條件有沒有年齡啊？上課還有事業體成立，這個是必要的條件，你要先有這些條件之後才能申請貸款。] , duration:[14.13s], offset:[5m42.87s]
2024-04-10 11:25:23.045 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，那我可以。] , duration:[1.65s], offset:[5m58.02s]
2024-04-10 11:25:31.582 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [忘記了，對呀，那就是說後續貸款核定的狀況就是剛剛有提到會回歸到這個負責人的債信狀況啊，需要審查，所以他也不是說申請一定會通過。] , duration:[15.2s], offset:[6m2.86s]
2024-04-10 11:25:38.839 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [那我想問說欸，如果假設申請比如說200萬，那它如果它是只有過跟不過的差別，還是有可能會是過，然後可能過150萬或者100萬這種這個會有可能對。] , duration:[14.04s], offset:[6m18.74s]
2024-04-10 11:25:51.306 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，所以他不會，就是說度不到200萬就直接不給你過就對了，對呀。就是比如說可能資金需求200萬，因為各種可能性都有啦，就是說銀行只願意貸喔，50萬給你也是有可能，只是說他覺得你們的風險比較高，或者是說他覺得你的債權保障不夠的話，也不與核彈這個都有可能的，那就是要看銀行，他們怎麼去做評估。] , duration:[24.45s], offset:[6m33.65s]
2024-04-10 11:25:58.672 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [哦，那我知道了，好對，因為因為青創貸款，他本身是貸款的方案，所以都還是要歸銀行那一套審核的程序啊，只是說在貸款的利率啊喔會比較低，比較優惠。] , duration:[13.67s], offset:[6m59.5s]
2024-04-10 11:26:04.192 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [搭了利率各家銀行都一樣，還是對他都是依照我們的政府規定的計算方式去做執行，那目前的話是1.42%。] , duration:[10.06s], offset:[7m14.11s]
2024-04-10 11:26:06.471 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [一定是啊，好目前的話是這樣子。] , duration:[3.81s], offset:[7m25.39s]
2024-04-10 11:26:09.387 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [這個有那個窗口，臺中有窗口，就是可以直接過去。] , duration:[4.85s], offset:[7m30.28s]
2024-04-10 11:26:10.693 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [就是填寫。] , duration:[1.15s], offset:[7m36.54s]
2024-04-10 11:26:15.284 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [就是網站上面去下載那個相關的表格，然後就把那個資料直接送到銀行端去做辦理就可以了。] , duration:[7.53s], offset:[7m39.26s]
2024-04-10 11:26:17.729 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，好，那我知道了對它的程序就是這樣。] , duration:[3.85s], offset:[7m47.91s]
2024-04-10 11:26:19.770 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，那如果，呃，我看他是。] , duration:[2.75s], offset:[7m53.47s]
2024-04-10 11:26:22.103 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [你如果超過100萬的話，是一定要有丹。] , duration:[3.42s], offset:[7m57.28s]
2024-04-10 11:26:23.524 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [寶品還是不一定？] , duration:[1.87s], offset:[8m1.93s]
2024-04-10 11:26:34.838 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [擔保品我們沒有規定，就是說銀行評估有需要整體保擔保品的話，那就是有可能需要擔保品，對我們在新莊貸款之後規定保人的部分而已，就是貸款金額在100萬以上以下用個人名義申請是不用增提保人的，但是超過100萬以上就是以一人為限。] , duration:[21.86s], offset:[8m4.51s]
2024-04-10 11:26:40.704 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [好，我們回到保人保人人數的這個規定，但是至於擔保品的話，我們沒有特別規定就是回歸銀行的那個授信狀況去做審核。] , duration:[11.18s], offset:[8m27.22s]
2024-04-10 11:26:47.056 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.4] azure_stt.go:541: recognized : text [喔，好，那我知道了嗯，好謝謝你喔，對呀，你們隨時有問題再來電都沒有關係好OK好謝謝你不會好再見。] , duration:[11.95s], offset:[8m39.5s]
2024-04-10 11:26:47.716 [DEBU] {4808f5fd2bcdc417fca0b432d1b654c1} [SonaMesh/internal/logic/azure.(*STT).RecognizeFile.func2.3] azure_stt.go:532: session stopped : ID = 0c6d0723e21c4b2c8d49ef7448a168a1
