2024-07-11 13:31:57.538 [DEBU] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性
2024-07-11 13:31:57.539 [DEBU] {308f3366a111e1173a0c2d6c79e65acb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 13:32:12.358 [DEBU] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性
2024-07-11 13:32:12.369 [DEBU] {0840b1d9a411e1173b0c2d6c4aacaf3a} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:326: Synthesis find text from cache , file is :./voc/2024-07-11/1720675917539866.wav
2024-07-11 13:32:25.394 [DEBU] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性
2024-07-11 13:32:25.397 [DEBU] {d82392e2a711e1173c0c2d6c7bd56d0b} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:326: Synthesis find text from cache , file is :./voc/2024-07-11/1720675917539866.wav
2024-07-11 13:32:36.128 [DEBU] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. 
2024-07-11 13:32:36.128 [DEBU] {88f18762aa11e1173d0c2d6c2913dc80} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 13:32:52.967 [DEBU] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. test
2024-07-11 13:32:52.967 [DEBU] {9016394eae11e1173e0c2d6cd10584fb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 13:47:01.709 [DEBU] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc
2024-07-11 13:47:01.710 [DEBU] {38a80feb7312e117353b951347dbb7c5} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 13:47:30.868 [DEBU] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性. abc def
2024-07-11 13:47:30.869 [DEBU] {b0132eb57a12e117363b951329a231e0} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 14:32:24.021 [DEBU] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:可编程性:PostgreSQL允许开发人员使用多种编程语言(如PL/pgSQL、PL/Python、PL/Perl等)编写存储过程和函数,增强了数据库的灵活性和可扩展性
2024-07-11 14:32:24.021 [DEBU] {f07d4dc1ed14e117cb0ccf55df0884bb} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 14:33:23.322 [DEBU] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 14:33:23.322 [DEBU] {50d72590fb14e117cc0ccf5508670268} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 14:34:41.190 [DEBU] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 14:34:41.190 [DEBU] {f8b11ab10d15e117cd0ccf55c4b971ed} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:326: Synthesis find text from cache , file is :./voc/2024-07-11/1720679603322735.wav
2024-07-11 14:34:49.505 [DEBU] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 14:34:49.506 [DEBU] {387318a10f15e117ce0ccf55ee0a2db7} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:326: Synthesis find text from cache , file is :./voc/2024-07-11/1720679603322735.wav
2024-07-11 17:00:50.434 [DEBU] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 17:00:50.435 [DEBU] {c89c8a70071de1171972d76a7cb05b05} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 17:01:15.657 [DEBU] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 17:01:15.657 [DEBU] {a8ac8c500d1de1171a72d76a0ff70882} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 17:07:57.271 [DEBU] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 17:07:57.272 [DEBU] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 17:07:57.274 [DEBU] {908014d26a1de1173dea5c7e20385762} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:340: Please check  the configuration , key "vendor.Azure_111111.TTS" 
2024-07-11 17:08:23.044 [DEBU] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 17:08:23.055 [DEBU] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 17:08:23.057 [DEBU] {b8c983d2701de1173eea5c7eb596c199} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:340: Please check  the configuration , key "vendor.Azure_111111_000.TTS" 
2024-07-11 17:11:22.642 [DEBU] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音模型。
2024-07-11 17:11:22.643 [DEBU] {b05573a39a1de11740ea5c7e264feb7b} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
2024-07-11 17:12:02.729 [DEBU] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:219: Find file from cache, Text:Azure AI 语音服务现已推出视频翻译功能。 有关详细信息，请参阅什么是视频翻译？。
个人声音现已推出正式版。 有关详细信息，请参阅什么是个人声音？。
Azure AI 语音服务支持 OpenAI 文本转语音声音。 有关详细信息，请参阅什么是 OpenAI 文本转语音声音？。
定制声音 API 可用于创建和管理专业和个人神经网络定制声音。
2024-07-11 17:12:02.729 [DEBU] {70a5c8f8a31de1172448c62dfc63256a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:239: The text not in cache...
