2024-01-31 14:31:51.100 [DEBU] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-31 14:31:51.102 [DEBU] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "recognition-domain",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "28da3915e25aaf1757937701f90fff2f"
}
2024-01-31 14:31:51.540 [DEBU] {28da3915e25aaf1757937701f90fff2f} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": -4,
	"err_msg": "Can't find domain index."
}
2024-01-31 14:39:00.576 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-31 14:39:00.583 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "9802ee13465baf17589377011ea6242b"
}
2024-01-31 14:39:01.944 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-65
	],
	"recog_nbest": [
		"科技已經深刻地改變了我們的生活"
	],
	"recog_result": "科技已經深刻地改變了我們的生活",
	"recog_time": 1231,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:03.069270"
}
2024-01-31 14:39:03.171 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-93
	],
	"recog_nbest": [
		"從便捷的智能手機到智能家居科技無處不在"
	],
	"recog_result": "從便捷的智能手機到智能家居科技無處不在",
	"recog_time": 2376,
	"result_index": 1,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:06.829270"
}
2024-01-31 14:39:07.340 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-179
	],
	"recog_nbest": [
		"員工智能的發展讓機器能夠學習和思考大數據的應用讓我們能夠從海量資訊中獲得有價值的見解"
	],
	"recog_result": "員工智能的發展讓機器能夠學習和思考大數據的應用讓我們能夠從海量資訊中獲得有價值的見解",
	"recog_time": 6627,
	"result_index": 2,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:11.749270"
}
2024-01-31 14:39:09.853 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-94
	],
	"recog_nbest": [
		"無人駕駛汽車的出現提供了更安全和高效的交通方式"
	],
	"recog_result": "無人駕駛汽車的出現提供了更安全和高效的交通方式",
	"recog_time": 9141,
	"result_index": 3,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:20.869269"
}
2024-01-31 14:39:10.901 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-86
	],
	"recog_nbest": [
		"區塊鏈技術創造了去中心化和可信的交易環境"
	],
	"recog_result": "區塊鏈技術創造了去中心化和可信的交易環境",
	"recog_time": 10177,
	"result_index": 4,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:26.229270"
}
2024-01-31 14:39:12.568 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-101
	],
	"recog_nbest": [
		"生命科學的進步帶來了新的治療方法和基因編輯的可能性"
	],
	"recog_result": "生命科學的進步帶來了新的治療方法和基因編輯的可能性",
	"recog_time": 11858,
	"result_index": 5,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:31.189270"
}
2024-01-31 14:39:14.951 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-113
	],
	"recog_nbest": [
		"虛擬現實何擴增現實改變了我們的娛樂和教育體驗"
	],
	"recog_result": "虛擬現實何擴增現實改變了我們的娛樂和教育體驗",
	"recog_time": 14242,
	"result_index": 6,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:36.709270"
}
2024-01-31 14:39:16.951 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-99
	],
	"recog_nbest": [
		"隨著科技的不斷進步我們迎來了一個充滿無限可能性的未來"
	],
	"recog_result": "隨著科技的不斷進步我們迎來了一個充滿無限可能性的未來",
	"recog_time": 16243,
	"result_index": 7,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:41.949270"
}
2024-01-31 14:39:20.123 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-129
	],
	"recog_nbest": [
		"我們期待著更多創新的科技產品和服務為我們的生活帶來便利和改善"
	],
	"recog_result": "我們期待著更多創新的科技產品和服務為我們的生活帶來便利和改善",
	"recog_time": 19413,
	"result_index": 8,
	"state": "result",
	"word_start_time": "2024-01-31 14:39:47.469269"
}
2024-01-31 14:39:20.255 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"state": "listening"
}
2024-01-31 14:39:20.256 [DEBU] {9802ee13465baf17589377011ea6242b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:629: End of document recognition...
2024-01-31 14:42:58.256 [DEBU] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-31 14:42:58.257 [DEBU] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).Start] cyberon_stt.go:403: {
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "recognition-domain",
	"isGetPartial": false,
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/L16;rate=16000",
	"uid": "9wbozm01kyecysork2gc7vc100m4nttl"
}
2024-01-31 14:42:58.369 [DEBU] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:232: Receive response: {
	"err_code": -4,
	"err_msg": "Can't find domain index."
}
2024-01-31 14:42:58.433 [DEBU] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:216: websocket: close 1005 (no status)
2024-01-31 14:42:58.433 [DEBU] {f008346a7d5baf17599377011eed9511} [SonaMesh/internal/logic/cyberon.(*STT).onMessage.func1] cyberon_stt.go:209: Leave receive message... 
