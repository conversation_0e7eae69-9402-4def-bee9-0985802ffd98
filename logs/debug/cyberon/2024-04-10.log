2024-04-10 12:00:58.990 [DEBU] {b0cfec3746cfc41707982e0931942642} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:00:58.991 [DEBU] {b0cfec3746cfc41707982e0931942642} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "b0cfec3746cfc41707982e0931942642"
}
2024-04-10 12:01:00.121 [DEBU] {b0cfec3746cfc41707982e0931942642} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-366
	],
	"recog_nbest": [
		"這是一段使用的"
	],
	"recog_result": "這是一段使用的",
	"recog_time": 1015,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:00:59.404235"
}
2024-04-10 12:01:00.385 [DEBU] {b0cfec3746cfc41707982e0931942642} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:01:00.385 [DEBU] {b0cfec3746cfc41707982e0931942642} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:637: End of document recognition...
2024-04-10 12:02:52.911 [DEBU] {48ce00be60cfc417b006865462b6388b} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:02:52.912 [DEBU] {48ce00be60cfc417b006865462b6388b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "48ce00be60cfc417b006865462b6388b"
}
2024-04-10 12:02:54.166 [DEBU] {48ce00be60cfc417b006865462b6388b} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-366
	],
	"recog_nbest": [
		"這是一段使用的"
	],
	"recog_result": "這是一段使用的",
	"recog_time": 1115,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:02:53.326850"
}
2024-04-10 12:03:58.692 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:03:58.697 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "f8c7230a70cfc4178158c616831937f2"
}
2024-04-10 12:04:01.106 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-105
	],
	"recog_nbest": [
		"把三萬服務中心您好比起您高興為您服務"
	],
	"recog_result": "把三萬服務中心您好比起您高興為您服務",
	"recog_time": 2290,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:03:59.633532"
}
2024-04-10 12:04:04.209 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-299
	],
	"recog_nbest": [
		"我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明"
	],
	"recog_result": "我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明",
	"recog_time": 5394,
	"result_index": 1,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:03.993531"
}
2024-04-10 12:04:08.741 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-410
	],
	"recog_nbest": [
		"喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下"
	],
	"recog_result": "喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下",
	"recog_time": 9925,
	"result_index": 2,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:11.433531"
}
2024-04-10 12:04:10.691 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-139
	],
	"recog_nbest": [
		"小星球小姐是從來電的"
	],
	"recog_result": "小星球小姐是從來電的",
	"recog_time": 11875,
	"result_index": 3,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:21.913531"
}
2024-04-10 12:04:12.052 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-42
	],
	"recog_nbest": [
		"因為台中"
	],
	"recog_result": "因為台中",
	"recog_time": 12763,
	"result_index": 4,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:26.873531"
}
2024-04-10 12:04:26.673 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-709
	],
	"recog_nbest": [
		"我跟那個邱小姐稍微解釋一下就是說清創貸款啊本身這個方案是沒有期限的限制啦那只是說因為從去年開始輕鬆貸款如果你有貨貸成功貸款一百萬以下的有那個最長五年的利息補貼嘛那如果是那個補貼的方案的話他是指要在三月底之前送件的案件才有這個資格去申請這個利息的補貼"
	],
	"recog_result": "我跟那個邱小姐稍微解釋一下就是說清創貸款啊本身這個方案是沒有期限的限制啦那只是說因為從去年開始輕鬆貸款如果你有貨貸成功貸款一百萬以下的有那個最長五年的利息補貼嘛那如果是那個補貼的方案的話他是指要在三月底之前送件的案件才有這個資格去申請這個利息的補貼",
	"recog_time": 27857,
	"result_index": 5,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:29.113531"
}
2024-04-10 12:04:30.415 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-235
	],
	"recog_nbest": [
		"好所以你超過三月底才申請新創貸款的話差別就是沒有那個一百萬五年的利息補貼"
	],
	"recog_result": "好所以你超過三月底才申請新創貸款的話差別就是沒有那個一百萬五年的利息補貼",
	"recog_time": 31601,
	"result_index": 6,
	"state": "result",
	"word_start_time": "2024-04-10 12:04:58.713531"
}
2024-04-10 12:04:32.033 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-101
	],
	"recog_nbest": [
		"哦啊如果沒有那個補貼的話是"
	],
	"recog_result": "哦啊如果沒有那個補貼的話是",
	"recog_time": 33220,
	"result_index": 7,
	"state": "result",
	"word_start_time": "2024-04-10 12:05:08.513531"
}
2024-04-10 12:04:53.075 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-241
	],
	"recog_nbest": [
		"哦就只有插在那個其實是一樣插在就領不到"
	],
	"recog_result": "哦就只有插在那個其實是一樣插在就領不到",
	"recog_time": 54259,
	"result_index": 8,
	"state": "result",
	"word_start_time": "2024-04-10 12:05:45.313282"
}
2024-04-10 12:04:54.425 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-21
	],
	"recog_nbest": [
		"補貼的費用"
	],
	"recog_result": "補貼的費用",
	"recog_time": 54513,
	"result_index": 9,
	"state": "result",
	"word_start_time": "2024-04-10 12:05:52.793281"
}
2024-04-10 12:04:56.802 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-212
	],
	"recog_nbest": [
		"了解那我想問一下說欸他申請這個是一定要先那個公司嗎"
	],
	"recog_result": "了解那我想問一下說欸他申請這個是一定要先那個公司嗎",
	"recog_time": 57986,
	"result_index": 10,
	"state": "result",
	"word_start_time": "2024-04-10 12:05:55.433281"
}
2024-04-10 12:04:58.310 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-35
	],
	"recog_nbest": [
		"那欸如果我們是"
	],
	"recog_result": "那欸如果我們是",
	"recog_time": 58821,
	"result_index": 11,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:04.273281"
}
2024-04-10 12:04:59.927 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-88
	],
	"recog_nbest": [
		"比如說是要加盟一些飲料店"
	],
	"recog_result": "比如說是要加盟一些飲料店",
	"recog_time": 60688,
	"result_index": 12,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:08.473281"
}
2024-04-10 12:05:02.259 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-108
	],
	"recog_nbest": [
		"那他的就是這間公司的負責人是"
	],
	"recog_result": "那他的就是這間公司的負責人是",
	"recog_time": 62486,
	"result_index": 13,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:12.473281"
}
2024-04-10 12:05:05.351 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-246
	],
	"recog_nbest": [
		"對如果假設有三個人都要申請貸款就可以先從公司嗎還是"
	],
	"recog_result": "對如果假設有三個人都要申請貸款就可以先從公司嗎還是",
	"recog_time": 65128,
	"result_index": 14,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:17.993281"
}
2024-04-10 12:05:12.396 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-123
	],
	"recog_nbest": [
		"喔所以就只有一個人對"
	],
	"recog_result": "喔所以就只有一個人對",
	"recog_time": 73583,
	"result_index": 15,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:39.753156"
}
2024-04-10 12:05:14.849 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-18
	],
	"recog_nbest": [
		"對對"
	],
	"recog_result": "對對",
	"recog_time": 73779,
	"result_index": 16,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:42.113156"
}
2024-04-10 12:05:16.595 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-33
	],
	"recog_nbest": [
		"如果超過一百萬的話是"
	],
	"recog_result": "如果超過一百萬的話是",
	"recog_time": 74366,
	"result_index": 17,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:44.073156"
}
2024-04-10 12:05:18.033 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-45
	],
	"recog_nbest": [
		"要大概什麼事"
	],
	"recog_result": "要大概什麼事",
	"recog_time": 74956,
	"result_index": 18,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:48.033156"
}
2024-04-10 12:05:34.550 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-1040
	],
	"recog_nbest": [
		"沒有他的條條件都是一樣的啊他都是要符合年齡上課以及事業體成立未超過五年那至於貸款金額你就是以資金需求的部分去做填寫嘛你會看到我們輕鬆貸款要點上面有分三個貸款用途喔準備金跟開辦費用最高兩百萬週轉性支出最高四百萬主要銀行端他們在審查的時候會去評估這個負責人的債信以及還款能力還有你實際貸款的用途是不是真的有需要這麼多"
	],
	"recog_result": "沒有他的條條件都是一樣的啊他都是要符合年齡上課以及事業體成立未超過五年那至於貸款金額你就是以資金需求的部分去做填寫嘛你會看到我們輕鬆貸款要點上面有分三個貸款用途喔準備金跟開辦費用最高兩百萬週轉性支出最高四百萬主要銀行端他們在審查的時候會去評估這個負責人的債信以及還款能力還有你實際貸款的用途是不是真的有需要這麼多",
	"recog_time": 95733,
	"result_index": 19,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:50.513156"
}
2024-04-10 12:05:36.039 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-115
	],
	"recog_nbest": [
		"銀行會去做一個全盤的考量"
	],
	"recog_result": "銀行會去做一個全盤的考量",
	"recog_time": 97226,
	"result_index": 20,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:28.313155"
}
2024-04-10 12:05:38.234 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-105
	],
	"recog_nbest": [
		"嗯最主要他們在考量的就是在還款來源的部分"
	],
	"recog_result": "嗯最主要他們在考量的就是在還款來源的部分",
	"recog_time": 99420,
	"result_index": 21,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:31.513155"
}
2024-04-10 12:05:40.287 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-98
	],
	"recog_nbest": [
		"就是負責人大概你說如果是"
	],
	"recog_result": "就是負責人大概你說如果是",
	"recog_time": 101473,
	"result_index": 22,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:38.753155"
}
2024-04-10 12:05:42.025 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-54
	],
	"recog_nbest": [
		"第一個那個兩百萬是什麼"
	],
	"recog_result": "第一個那個兩百萬是什麼",
	"recog_time": 102164,
	"result_index": 23,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:43.873155"
}
2024-04-10 12:05:43.713 [DEBU] {f8c7230a70cfc4178158c616831937f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-38
	],
	"recog_nbest": [
		"準備金跟開辦費用"
	],
	"recog_result": "準備金跟開辦費用",
	"recog_time": 102477,
	"result_index": 24,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:46.073155"
}
2024-04-10 12:06:20.811 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:06:20.814 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "d081452191cfc41718886a5de2e3c703"
}
2024-04-10 12:06:23.056 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-105
	],
	"recog_nbest": [
		"把三萬服務中心您好比起您高興為您服務"
	],
	"recog_result": "把三萬服務中心您好比起您高興為您服務",
	"recog_time": 2132,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:21.745701"
}
2024-04-10 12:06:36.440 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-299
	],
	"recog_nbest": [
		"我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明"
	],
	"recog_result": "我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明",
	"recog_time": 6665,
	"result_index": 1,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:26.105700"
}
2024-04-10 12:06:51.078 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-410
	],
	"recog_nbest": [
		"喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下"
	],
	"recog_result": "喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下",
	"recog_time": 13546,
	"result_index": 2,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:33.545700"
}
2024-04-10 12:07:08.156 [DEBU] {d081452191cfc41718886a5de2e3c703} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-139
	],
	"recog_nbest": [
		"小星球小姐是從來電的"
	],
	"recog_result": "小星球小姐是從來電的",
	"recog_time": 16658,
	"result_index": 3,
	"state": "result",
	"word_start_time": "2024-04-10 12:06:44.025700"
}
2024-04-10 12:07:58.866 [DEBU] {503de8f9a7cfc417bbfa21763859626e} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:07:58.870 [DEBU] {503de8f9a7cfc417bbfa21763859626e} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "503de8f9a7cfc417bbfa21763859626e"
}
2024-04-10 12:08:00.506 [DEBU] {503de8f9a7cfc417bbfa21763859626e} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-366
	],
	"recog_nbest": [
		"這是一段使用的"
	],
	"recog_result": "這是一段使用的",
	"recog_time": 1515,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:07:59.278373"
}
2024-04-10 12:08:00.902 [DEBU] {503de8f9a7cfc417bbfa21763859626e} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:08:00.903 [DEBU] {503de8f9a7cfc417bbfa21763859626e} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:637: End of document recognition...
2024-04-10 12:08:16.560 [DEBU] {a04e8619accfc417bcfa2176807554ef} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:08:16.560 [DEBU] {a04e8619accfc417bcfa2176807554ef} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "a04e8619accfc417bcfa2176807554ef"
}
2024-04-10 12:08:17.199 [DEBU] {a04e8619accfc417bcfa2176807554ef} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-59
	],
	"recog_nbest": [
		"附近的STARBUCKS"
	],
	"recog_result": "附近的STARBUCKS",
	"recog_time": 521,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:17.565515"
}
2024-04-10 12:08:17.259 [DEBU] {a04e8619accfc417bcfa2176807554ef} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:08:17.260 [DEBU] {a04e8619accfc417bcfa2176807554ef} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:637: End of document recognition...
2024-04-10 12:08:24.557 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:98: {
	"err_code": 0,
	"state": "listening"
}
2024-04-10 12:08:24.558 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:552: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/wav;rate=16000",
	"uid": "4862bef1adcfc417bdfa2176deccab73"
}
2024-04-10 12:08:28.264 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-105
	],
	"recog_nbest": [
		"把三萬服務中心您好比起您高興為您服務"
	],
	"recog_result": "把三萬服務中心您好比起您高興為您服務",
	"recog_time": 3596,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:25.477154"
}
2024-04-10 12:08:32.290 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-299
	],
	"recog_nbest": [
		"我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明"
	],
	"recog_result": "我要那個我是我那個創業貸款是問問這邊嗎對這邊可以幫您做說明",
	"recog_time": 7621,
	"result_index": 1,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:29.837153"
}
2024-04-10 12:08:38.689 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-410
	],
	"recog_nbest": [
		"喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下"
	],
	"recog_result": "喔那個我想問說那我看網路上小姐申請讓三月三十一日後面就不能擔心嘛喔跟小姐請教一下",
	"recog_time": 14016,
	"result_index": 2,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:37.277153"
}
2024-04-10 12:08:41.436 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-139
	],
	"recog_nbest": [
		"小星球小姐是從來電的"
	],
	"recog_result": "小星球小姐是從來電的",
	"recog_time": 16768,
	"result_index": 3,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:47.757153"
}
2024-04-10 12:08:42.709 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-42
	],
	"recog_nbest": [
		"因為台中"
	],
	"recog_result": "因為台中",
	"recog_time": 18027,
	"result_index": 4,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:52.717153"
}
2024-04-10 12:09:02.740 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-709
	],
	"recog_nbest": [
		"我跟那個邱小姐稍微解釋一下就是說清創貸款啊本身這個方案是沒有期限的限制啦那只是說因為從去年開始輕鬆貸款如果你有貨貸成功貸款一百萬以下的有那個最長五年的利息補貼嘛那如果是那個補貼的方案的話他是指要在三月底之前送件的案件才有這個資格去申請這個利息的補貼"
	],
	"recog_result": "我跟那個邱小姐稍微解釋一下就是說清創貸款啊本身這個方案是沒有期限的限制啦那只是說因為從去年開始輕鬆貸款如果你有貨貸成功貸款一百萬以下的有那個最長五年的利息補貼嘛那如果是那個補貼的方案的話他是指要在三月底之前送件的案件才有這個資格去申請這個利息的補貼",
	"recog_time": 38070,
	"result_index": 5,
	"state": "result",
	"word_start_time": "2024-04-10 12:08:54.957153"
}
2024-04-10 12:09:08.122 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-235
	],
	"recog_nbest": [
		"好所以你超過三月底才申請新創貸款的話差別就是沒有那個一百萬五年的利息補貼"
	],
	"recog_result": "好所以你超過三月底才申請新創貸款的話差別就是沒有那個一百萬五年的利息補貼",
	"recog_time": 43454,
	"result_index": 6,
	"state": "result",
	"word_start_time": "2024-04-10 12:09:24.557153"
}
2024-04-10 12:09:10.661 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-101
	],
	"recog_nbest": [
		"哦啊如果沒有那個補貼的話是"
	],
	"recog_result": "哦啊如果沒有那個補貼的話是",
	"recog_time": 45993,
	"result_index": 7,
	"state": "result",
	"word_start_time": "2024-04-10 12:09:34.357153"
}
2024-04-10 12:09:37.046 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-241
	],
	"recog_nbest": [
		"哦就只有插在那個其實是一樣插在就領不到"
	],
	"recog_result": "哦就只有插在那個其實是一樣插在就領不到",
	"recog_time": 72379,
	"result_index": 8,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:11.156903"
}
2024-04-10 12:09:38.343 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-21
	],
	"recog_nbest": [
		"補貼的費用"
	],
	"recog_result": "補貼的費用",
	"recog_time": 72702,
	"result_index": 9,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:18.636903"
}
2024-04-10 12:09:42.666 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-212
	],
	"recog_nbest": [
		"了解那我想問一下說欸他申請這個是一定要先那個公司嗎"
	],
	"recog_result": "了解那我想問一下說欸他申請這個是一定要先那個公司嗎",
	"recog_time": 77998,
	"result_index": 10,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:21.276903"
}
2024-04-10 12:09:44.082 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-35
	],
	"recog_nbest": [
		"那欸如果我們是"
	],
	"recog_result": "那欸如果我們是",
	"recog_time": 79015,
	"result_index": 11,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:30.116903"
}
2024-04-10 12:09:46.418 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-88
	],
	"recog_nbest": [
		"比如說是要加盟一些飲料店"
	],
	"recog_result": "比如說是要加盟一些飲料店",
	"recog_time": 81748,
	"result_index": 12,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:34.316903"
}
2024-04-10 12:09:49.118 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-108
	],
	"recog_nbest": [
		"那他的就是這間公司的負責人是"
	],
	"recog_result": "那他的就是這間公司的負責人是",
	"recog_time": 84451,
	"result_index": 13,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:38.316903"
}
2024-04-10 12:09:52.437 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-246
	],
	"recog_nbest": [
		"對如果假設有三個人都要申請貸款就可以先從公司嗎還是"
	],
	"recog_result": "對如果假設有三個人都要申請貸款就可以先從公司嗎還是",
	"recog_time": 87770,
	"result_index": 14,
	"state": "result",
	"word_start_time": "2024-04-10 12:10:43.836903"
}
2024-04-10 12:10:04.519 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-123
	],
	"recog_nbest": [
		"喔所以就只有一個人對"
	],
	"recog_result": "喔所以就只有一個人對",
	"recog_time": 99852,
	"result_index": 15,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:05.596778"
}
2024-04-10 12:10:06.319 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-18
	],
	"recog_nbest": [
		"對對"
	],
	"recog_result": "對對",
	"recog_time": 100102,
	"result_index": 16,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:07.956778"
}
2024-04-10 12:10:07.760 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-33
	],
	"recog_nbest": [
		"如果超過一百萬的話是"
	],
	"recog_result": "如果超過一百萬的話是",
	"recog_time": 100907,
	"result_index": 17,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:09.916778"
}
2024-04-10 12:10:09.212 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-45
	],
	"recog_nbest": [
		"要大概什麼事"
	],
	"recog_result": "要大概什麼事",
	"recog_time": 101896,
	"result_index": 18,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:13.876778"
}
2024-04-10 12:10:37.439 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-1040
	],
	"recog_nbest": [
		"沒有他的條條件都是一樣的啊他都是要符合年齡上課以及事業體成立未超過五年那至於貸款金額你就是以資金需求的部分去做填寫嘛你會看到我們輕鬆貸款要點上面有分三個貸款用途喔準備金跟開辦費用最高兩百萬週轉性支出最高四百萬主要銀行端他們在審查的時候會去評估這個負責人的債信以及還款能力還有你實際貸款的用途是不是真的有需要這麼多"
	],
	"recog_result": "沒有他的條條件都是一樣的啊他都是要符合年齡上課以及事業體成立未超過五年那至於貸款金額你就是以資金需求的部分去做填寫嘛你會看到我們輕鬆貸款要點上面有分三個貸款用途喔準備金跟開辦費用最高兩百萬週轉性支出最高四百萬主要銀行端他們在審查的時候會去評估這個負責人的債信以及還款能力還有你實際貸款的用途是不是真的有需要這麼多",
	"recog_time": 132767,
	"result_index": 19,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:16.356778"
}
2024-04-10 12:10:39.881 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-115
	],
	"recog_nbest": [
		"銀行會去做一個全盤的考量"
	],
	"recog_result": "銀行會去做一個全盤的考量",
	"recog_time": 135215,
	"result_index": 20,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:54.156777"
}
2024-04-10 12:10:43.299 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-105
	],
	"recog_nbest": [
		"嗯最主要他們在考量的就是在還款來源的部分"
	],
	"recog_result": "嗯最主要他們在考量的就是在還款來源的部分",
	"recog_time": 138631,
	"result_index": 21,
	"state": "result",
	"word_start_time": "2024-04-10 12:11:57.356777"
}
2024-04-10 12:10:46.466 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-98
	],
	"recog_nbest": [
		"就是負責人大概你說如果是"
	],
	"recog_result": "就是負責人大概你說如果是",
	"recog_time": 141800,
	"result_index": 22,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:04.596777"
}
2024-04-10 12:10:47.732 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-54
	],
	"recog_nbest": [
		"第一個那個兩百萬是什麼"
	],
	"recog_result": "第一個那個兩百萬是什麼",
	"recog_time": 143004,
	"result_index": 23,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:09.716777"
}
2024-04-10 12:10:49.192 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-38
	],
	"recog_nbest": [
		"準備金跟開辦費用"
	],
	"recog_result": "準備金跟開辦費用",
	"recog_time": 143459,
	"result_index": 24,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:11.916777"
}
2024-04-10 12:10:58.477 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-387
	],
	"recog_nbest": [
		"喔就剛開店的資金嘛對就是但是你還是要有公司行號設立我們準備健康開辦費用設立後的八個月內你所需的費用比如說裝潢費啦或者是一些水電"
	],
	"recog_result": "喔就剛開店的資金嘛對就是但是你還是要有公司行號設立我們準備健康開辦費用設立後的八個月內你所需的費用比如說裝潢費啦或者是一些水電",
	"recog_time": 153806,
	"result_index": 25,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:15.196777"
}
2024-04-10 12:11:03.358 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-182
	],
	"recog_nbest": [
		"我是購買一些設備只要是八個月以內的話都可以先從準備金的費用心提出申請"
	],
	"recog_result": "我是購買一些設備只要是八個月以內的話都可以先從準備金的費用心提出申請",
	"recog_time": 158679,
	"result_index": 26,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:31.316777"
}
2024-04-10 12:11:06.888 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-150
	],
	"recog_nbest": [
		"啊那個週轉金的話不少週轉金金額可以比較高"
	],
	"recog_result": "啊那個週轉金的話不少週轉金金額可以比較高",
	"recog_time": 162221,
	"result_index": 27,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:39.276777"
}
2024-04-10 12:11:12.342 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-297
	],
	"recog_nbest": [
		"那昨晚金因為就是貸款用途因為有些人他可能成立超過八個月了那他週轉的需求可能比較高"
	],
	"recog_result": "那昨晚金因為就是貸款用途因為有些人他可能成立超過八個月了那他週轉的需求可能比較高",
	"recog_time": 167675,
	"result_index": 28,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:44.876777"
}
2024-04-10 12:11:19.478 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-274
	],
	"recog_nbest": [
		"對但是還是要看實際的經你貸款的需求是多少他也不是說四百萬就一次貸四百萬給你他是要看你實際的貸款的需求"
	],
	"recog_result": "對但是還是要看實際的經你貸款的需求是多少他也不是說四百萬就一次貸四百萬給你他是要看你實際的貸款的需求",
	"recog_time": 174811,
	"result_index": 29,
	"state": "result",
	"word_start_time": "2024-04-10 12:12:53.076777"
}
2024-04-10 12:11:25.051 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-332
	],
	"recog_nbest": [
		"喔因為我想說如果我辦那個飲料店加盟就要兩百多萬我如果開放費兩百萬加好像不公"
	],
	"recog_result": "喔因為我想說如果我辦那個飲料店加盟就要兩百多萬我如果開放費兩百萬加好像不公",
	"recog_time": 180382,
	"result_index": 30,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:04.916777"
}
2024-04-10 12:11:33.516 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-397
	],
	"recog_nbest": [
		"因為如果你全部的心都用貸款相對的你這樣子的風險也是比較高的因為我們現在創業貸款是屬於你要創業的之後那後續部署再用這個創業貸款來做"
	],
	"recog_result": "因為如果你全部的心都用貸款相對的你這樣子的風險也是比較高的因為我們現在創業貸款是屬於你要創業的之後那後續部署再用這個創業貸款來做",
	"recog_time": 188848,
	"result_index": 31,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:13.436777"
}
2024-04-10 12:11:34.745 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-36
	],
	"recog_nbest": [
		"嗯對啊"
	],
	"recog_result": "嗯對啊",
	"recog_time": 189471,
	"result_index": 32,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:26.916777"
}
2024-04-10 12:11:36.279 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-94
	],
	"recog_nbest": [
		"所以才會說一定要有公司或行號的設立"
	],
	"recog_result": "所以才會說一定要有公司或行號的設立",
	"recog_time": 190958,
	"result_index": 33,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:28.056214"
}
2024-04-10 12:11:39.020 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-154
	],
	"recog_nbest": [
		"那如果他要加盟的話就是請加盟那邊協助辦理"
	],
	"recog_result": "那如果他要加盟的話就是請加盟那邊協助辦理",
	"recog_time": 194353,
	"result_index": 34,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:33.096214"
}
2024-04-10 12:11:42.704 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-40
	],
	"recog_nbest": [
		"對啊就是"
	],
	"recog_result": "對啊就是",
	"recog_time": 198036,
	"result_index": 35,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:45.026152"
}
2024-04-10 12:11:44.762 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-170
	],
	"recog_nbest": [
		"鴨絨要自己再創一個公司然後用那個在下去"
	],
	"recog_result": "鴨絨要自己再創一個公司然後用那個在下去",
	"recog_time": 200095,
	"result_index": 36,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:47.586152"
}
2024-04-10 12:11:52.860 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-557
	],
	"recog_nbest": [
		"對啊就比如說萬花很多50嵐那每一家五十他都有工商對你就是要工商之後做到那麼就要擔心工商登記然後再去辦才能"
	],
	"recog_result": "對啊就比如說萬花很多50嵐那每一家五十他都有工商對你就是要工商之後做到那麼就要擔心工商登記然後再去辦才能",
	"recog_time": 208191,
	"result_index": 37,
	"state": "result",
	"word_start_time": "2024-04-10 12:13:51.706152"
}
2024-04-10 12:11:58.550 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-307
	],
	"recog_nbest": [
		"就是我剛剛最一開始講的那個三個條件有沒有年齡啊上課還有失業期成立這個是必要的條件你要先有這些條件之後才能申請貸款"
	],
	"recog_result": "就是我剛剛最一開始講的那個三個條件有沒有年齡啊上課還有失業期成立這個是必要的條件你要先有這些條件之後才能申請貸款",
	"recog_time": 213882,
	"result_index": 38,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:08.826152"
}
2024-04-10 12:11:59.867 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-95
	],
	"recog_nbest": [
		"好那我給你"
	],
	"recog_result": "好那我給你",
	"recog_time": 214304,
	"result_index": 39,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:22.986152"
}
2024-04-10 12:12:01.549 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-20
	],
	"recog_nbest": [
		"嗯"
	],
	"recog_result": "嗯",
	"recog_time": 214512,
	"result_index": 40,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:25.306152"
}
2024-04-10 12:12:06.250 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-389
	],
	"recog_nbest": [
		"兩忘記沒關係對啊那就是說後續貸款否定的狀況就是剛剛有提到會回歸到這個負責人的在心狀況啊需要審查所以他也不是申請一定會通過"
	],
	"recog_result": "兩忘記沒關係對啊那就是說後續貸款否定的狀況就是剛剛有提到會回歸到這個負責人的在心狀況啊需要審查所以他也不是申請一定會通過",
	"recog_time": 221581,
	"result_index": 41,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:27.746152"
}
2024-04-10 12:12:12.755 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-465
	],
	"recog_nbest": [
		"那我想問說欸如果假設有申請比如說兩百萬那他如果他是只有過跟不夠的差別還是有可能會是過然後可能過一百五十萬或是一百萬這種這個會有"
	],
	"recog_result": "那我想問說欸如果假設有申請比如說兩百萬那他如果他是只有過跟不夠的差別還是有可能會是過然後可能過一百五十萬或是一百萬這種這個會有",
	"recog_time": 228085,
	"result_index": 42,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:43.586152"
}
2024-04-10 12:12:26.668 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-835
	],
	"recog_nbest": [
		"喔所以他不會就是額度不到兩百萬就直接不給你做就對了對啊就是比如說可能最近需求兩百萬因為各種可能性都有啦就是說喔願意貸五十萬給你有可能只是說他覺得風險比較高說他覺得債權保障的話也不予核貸這個都有那就是要看"
	],
	"recog_result": "喔所以他不會就是額度不到兩百萬就直接不給你做就對了對啊就是比如說可能最近需求兩百萬因為各種可能性都有啦就是說喔願意貸五十萬給你有可能只是說他覺得風險比較高說他覺得債權保障的話也不予核貸這個都有那就是要看",
	"recog_time": 241999,
	"result_index": 43,
	"state": "result",
	"word_start_time": "2024-04-10 12:14:58.546152"
}
2024-04-10 12:12:29.427 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-276
	],
	"recog_nbest": [
		"我們問老師好好對因為因為青壯貸款他本身是貸款的方案所以"
	],
	"recog_result": "我們問老師好好對因為因為青壯貸款他本身是貸款的方案所以",
	"recog_time": 244760,
	"result_index": 44,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:24.346152"
}
2024-04-10 12:12:32.700 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-202
	],
	"recog_nbest": [
		"都還是要回銀行那一套審核的程序只是說在貸款的利率啊喔會比較低比較優惠"
	],
	"recog_result": "都還是要回銀行那一套審核的程序只是說在貸款的利率啊喔會比較低比較優惠",
	"recog_time": 248032,
	"result_index": 45,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:30.226152"
}
2024-04-10 12:12:35.104 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-240
	],
	"recog_nbest": [
		"大家的命運是各家銀行都一樣還是對就是依照我們"
	],
	"recog_result": "大家的命運是各家銀行都一樣還是對就是依照我們",
	"recog_time": 250436,
	"result_index": 46,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:38.946152"
}
2024-04-10 12:12:37.186 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-133
	],
	"recog_nbest": [
		"規定的計算方式去做執行那目前的話是一點四二PERCENT"
	],
	"recog_result": "規定的計算方式去做執行那目前的話是一點四二PERCENT",
	"recog_time": 252518,
	"result_index": 47,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:43.855964"
}
2024-04-10 12:12:38.773 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-146
	],
	"recog_nbest": [
		"一點四喔好目前的話他"
	],
	"recog_result": "一點四喔好目前的話他",
	"recog_time": 254106,
	"result_index": 48,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:50.175964"
}
2024-04-10 12:12:41.018 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-103
	],
	"recog_nbest": [
		"這個有那個窗口台中有窗口是是是可以直接過去"
	],
	"recog_result": "這個有那個窗口台中有窗口是是是可以直接過去",
	"recog_time": 256350,
	"result_index": 49,
	"state": "result",
	"word_start_time": "2024-04-10 12:15:55.095964"
}
2024-04-10 12:12:42.294 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-27
	],
	"recog_nbest": [
		"就是填寫"
	],
	"recog_result": "就是填寫",
	"recog_time": 256747,
	"result_index": 50,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:01.415964"
}
2024-04-10 12:12:45.306 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-262
	],
	"recog_nbest": [
		"那還是就是網站上面去下載那個相關的表格然後就把那個資料直接送到銀行端去做辦理"
	],
	"recog_result": "那還是就是網站上面去下載那個相關的表格然後就把那個資料直接送到銀行端去做辦理",
	"recog_time": 260638,
	"result_index": 51,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:03.255964"
}
2024-04-10 12:12:46.684 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-171
	],
	"recog_nbest": [
		"好好好那我到了對"
	],
	"recog_result": "好好好那我到了對",
	"recog_time": 261446,
	"result_index": 52,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:12.815964"
}
2024-04-10 12:12:48.141 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-39
	],
	"recog_nbest": [
		"這程序就是這樣子"
	],
	"recog_result": "這程序就是這樣子",
	"recog_time": 261834,
	"result_index": 53,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:15.295964"
}
2024-04-10 12:12:49.729 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-65
	],
	"recog_nbest": [
		"好那如果欸我看他是"
	],
	"recog_result": "好那如果欸我看他是",
	"recog_time": 262788,
	"result_index": 54,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:18.455964"
}
2024-04-10 12:12:51.299 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-64
	],
	"recog_nbest": [
		"欸如果超過一百萬的話是一定要有三"
	],
	"recog_result": "欸如果超過一百萬的話是一定要有三",
	"recog_time": 264496,
	"result_index": 55,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:22.175964"
}
2024-04-10 12:12:52.771 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-46
	],
	"recog_nbest": [
		"高品還是不一定"
	],
	"recog_result": "高品還是不一定",
	"recog_time": 265203,
	"result_index": 56,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:26.815964"
}
2024-04-10 12:13:01.008 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-561
	],
	"recog_nbest": [
		"擔保品我們沒有規定就是說銀行評估有需要整體保擔保品的話那就是有可能需要擔保品對我們在西裝貸款之後規定保人的部分而已就是貸款金額在一百萬以上以下用個人名義申請是不用整體保人的但是超過一百萬以上就是以一人為限"
	],
	"recog_result": "擔保品我們沒有規定就是說銀行評估有需要整體保擔保品的話那就是有可能需要擔保品對我們在西裝貸款之後規定保人的部分而已就是貸款金額在一百萬以上以下用個人名義申請是不用整體保人的但是超過一百萬以上就是以一人為限",
	"recog_time": 276339,
	"result_index": 57,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:29.335964"
}
2024-04-10 12:13:03.037 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-210
	],
	"recog_nbest": [
		"好我們是老人老人人"
	],
	"recog_result": "好我們是老人老人人",
	"recog_time": 277592,
	"result_index": 58,
	"state": "result",
	"word_start_time": "2024-04-10 12:16:52.135965"
}
2024-04-10 12:13:06.930 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-130
	],
	"recog_nbest": [
		"喔好那我知道了"
	],
	"recog_result": "喔好那我知道了",
	"recog_time": 282258,
	"result_index": 59,
	"state": "result",
	"word_start_time": "2024-04-10 12:17:04.405777"
}
2024-04-10 12:13:09.206 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-194
	],
	"recog_nbest": [
		"好那現在如果你對啊你們隨時有問題再來電都沒有關係"
	],
	"recog_result": "好那現在如果你對啊你們隨時有問題再來電都沒有關係",
	"recog_time": 284537,
	"result_index": 60,
	"state": "result",
	"word_start_time": "2024-04-10 12:17:07.045777"
}
2024-04-10 12:13:10.981 [DEBU] {4862bef1adcfc417bdfa2176deccab73} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:635: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-29
	],
	"recog_nbest": [
		"好OK"
	],
	"recog_result": "好OK",
	"recog_time": 284824,
	"result_index": 61,
	"state": "result",
	"word_start_time": "2024-04-10 12:17:11.725777"
}
