2024-04-01 11:39:59.425 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:97: {
	"err_code": 0,
	"state": "listening"
}
2024-04-01 11:39:59.425 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).Start] cyberon_stt.go:401: {
	"action": "start",
	"bIsDoEPD": true,
	"bIsDoPR": true,
	"domain": "freeSTT-zh-TW",
	"isGetPartial": false,
	"nBestNum": 1,
	"platform": "web",
	"token": "2B0aWlQu8V1OX0YragC1t5prfNVDzL4OndyjLDIGED4JotQ8F5djueIKn_NfIg7Rxop5tdkYgCr-H3-SeVTB6ykrH1azXdrpJucXVNUdgM_bSwD-qqaHJzXr9tLf7AvR",
	"type": "audio/L16;rate=16000",
	"uid": "1joxdy21vapd08h2otsboy01001y0ked"
}
2024-04-01 11:40:04.678 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-45
	],
	"recog_nbest": [
		"大哥你好"
	],
	"recog_result": "大哥你好",
	"recog_time": 5137,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:02.633857"
}
2024-04-01 11:40:14.662 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-211
	],
	"recog_nbest": [
		"小姐我想要反應一下我買那個整本的那個筆記本會不會有咖啡券嗎是"
	],
	"recog_result": "小姐我想要反應一下我買那個整本的那個筆記本會不會有咖啡券嗎是",
	"recog_time": 15120,
	"result_index": 1,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:05.953857"
}
2024-04-01 11:40:17.700 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-88
	],
	"recog_nbest": [
		"那你們那個日期啊是這麼"
	],
	"recog_result": "那你們那個日期啊是這麼",
	"recog_time": 18160,
	"result_index": 2,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:13.193857"
}
2024-04-01 11:40:17.874 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-78
	],
	"recog_nbest": [
		"小我以為"
	],
	"recog_result": "小我以為",
	"recog_time": 18334,
	"result_index": 3,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:17.113857"
}
2024-04-01 11:40:23.072 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-152
	],
	"recog_nbest": [
		"知道十二月三十一號就是到十二月三十號"
	],
	"recog_result": "知道十二月三十一號就是到十二月三十號",
	"recog_time": 23532,
	"result_index": 4,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:17.793857"
}
2024-04-01 11:40:24.161 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-73
	],
	"recog_nbest": [
		"對因為我們往年每一年的"
	],
	"recog_result": "對因為我們往年每一年的",
	"recog_time": 24622,
	"result_index": 5,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:22.513857"
}
2024-04-01 11:40:27.919 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-69
	],
	"recog_nbest": [
		"券他都是到十二月三十號"
	],
	"recog_result": "券他都是到十二月三十號",
	"recog_time": 28378,
	"result_index": 6,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:24.183794"
}
2024-04-01 11:40:32.769 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-92
	],
	"recog_nbest": [
		"所以券的使用期限確實是到十二月三十"
	],
	"recog_result": "所以券的使用期限確實是到十二月三十",
	"recog_time": 33226,
	"result_index": 7,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:27.583794"
}
2024-04-01 11:40:39.250 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-101
	],
	"recog_nbest": [
		"那可是他一天為什麼你們門市又不肯讓我"
	],
	"recog_result": "那可是他一天為什麼你們門市又不肯讓我",
	"recog_time": 39710,
	"result_index": 8,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:32.463794"
}
2024-04-01 11:40:39.522 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-35
	],
	"recog_nbest": [
		"則底啊"
	],
	"recog_result": "則底啊",
	"recog_time": 39983,
	"result_index": 9,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:37.543794"
}
2024-04-01 11:40:42.868 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-154
	],
	"recog_nbest": [
		"小姐我這邊還買了很多杯買"
	],
	"recog_result": "小姐我這邊還買了很多杯買",
	"recog_time": 43325,
	"result_index": 10,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:39.263794"
}
2024-04-01 11:40:44.019 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-75
	],
	"recog_nbest": [
		"十幾杯"
	],
	"recog_result": "十幾杯",
	"recog_time": 44478,
	"result_index": 11,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:42.063794"
}
2024-04-01 11:40:53.643 [DEBU] {00dea9d8e70ac2175f3a076413a44217} [SonaMesh/internal/logic/cyberon.(*STT).onMessage] cyberon_stt.go:233: Receive response: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-246
	],
	"recog_nbest": [
		"小姐不好意思因為這不是差差幾千而是因為他就是過期門市就沒辦法收卷了"
	],
	"recog_result": "小姐不好意思因為這不是差差幾千而是因為他就是過期門市就沒辦法收卷了",
	"recog_time": 54103,
	"result_index": 12,
	"state": "result",
	"word_start_time": "2024-04-01 11:40:44.423794"
}
