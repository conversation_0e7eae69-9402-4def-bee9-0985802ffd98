2024-01-19 11:39:15.726 [DEBU] {98c2a04680a2ab17f853d55df68082c2} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "<PERSON>",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"token": "None",
	"uid": "1233",
	"vbr_quality": 4
} 
2024-01-19 11:41:21.835 [DEBU] {7831d7a39da2ab17f953d55d0a935ad7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "<PERSON>",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"token": "None",
	"uid": "1233",
	"vbr_quality": 4
} 
2024-01-19 11:42:36.194 [DEBU] {b07dcef3aea2ab17fa53d55d8644daf7} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"token": "None",
	"uid": "1233",
	"vbr_quality": 4
} 
2024-01-19 11:43:02.863 [DEBU] {8860a529b5a2ab17fb53d55d0da2a695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"token": "None",
	"uid": "1233",
	"vbr_quality": 4
} 
2024-01-19 11:43:41.636 [DEBU] {589b6730bea2ab179cd5f44e99e12695} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 这是一个tts的cyberon 测试",
	"token": "None",
	"uid": "1233",
	"vbr_quality": 4
} 
2024-01-19 12:04:01.532 [DEBU] {a8b49d37daa3ab17bf482e1a7c0bd834} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": "这是一个cyberon tts的测试",
	"token": "None",
	"uid": "111",
	"vbr_quality": 4
} 
2024-01-19 13:49:00.958 [DEBU] {d0e507eb94a9ab178422210fd5aa1351} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": "tts測試",
	"token": "None",
	"uid": "1111",
	"vbr_quality": 4
} 
2024-01-19 13:49:25.365 [DEBU] {28850c999aa9ab178522210f05226da0} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": "tts測試",
	"token": "None",
	"uid": "1111",
	"vbr_quality": 4
} 
2024-01-19 15:26:48.767 [DEBU] {1847ed1eebaeab17b3241d64d1c5baf6} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 這是一個tts測試",
	"token": "None",
	"uid": "",
	"vbr_quality": 4
} 
2024-01-19 15:32:06.953 [DEBU] {7820ae3435afab17a1e7a421fc6965b5} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:148: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 這是一個tts測試",
	"token": "None",
	"uid": "",
	"vbr_quality": 4
} 
2024-01-19 15:36:13.347 [DEBU] {0093baa55dafab173641190e362dc83b} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:148: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 這是一個tts測試",
	"token": "None",
	"uid": "",
	"vbr_quality": 4
} 
2024-01-19 15:39:26.119 [DEBU] {689e614981afab173741190e1bc400da} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:148: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 這是一個tts測試",
	"token": "None",
	"uid": "",
	"vbr_quality": 4
} 
