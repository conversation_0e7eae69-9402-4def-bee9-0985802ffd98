2024-01-16 15:31:15.884 [DEBU] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 15:31:15.891 [DEBU] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:557: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=16000",
	"uid": "e804828b6bc3aa1748ad98272673df39"
}
2024-01-16 15:31:16.102 [DEBU] {e804828b6bc3aa1748ad98272673df39} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:628: {
	"err_code": -2,
	"err_msg": "不支援的採樣率。"
}
2024-01-16 15:34:56.504 [DEBU] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 15:34:56.504 [DEBU] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:557: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=16000",
	"uid": "48535fef9ec3aa1749ad9827505b409c"
}
2024-01-16 15:34:56.704 [DEBU] {48535fef9ec3aa1749ad9827505b409c} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:628: {
	"err_code": -2,
	"err_msg": "不支援的採樣率。"
}
2024-01-16 16:04:17.429 [DEBU] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:04:17.431 [DEBU] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=24000",
	"uid": "f0f0ceee38c5aa177b5c761f16faadac"
}
2024-01-16 16:04:17.663 [DEBU] {f0f0ceee38c5aa177b5c761f16faadac} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": -2,
	"err_msg": "不支援的採樣率。"
}
2024-01-16 16:06:48.865 [DEBU] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:06:48.866 [DEBU] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=8000",
	"uid": "f09f7e305cc5aa177c5c761fb429c1f2"
}
2024-01-16 16:06:48.942 [DEBU] {f09f7e305cc5aa177c5c761fb429c1f2} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": -2,
	"err_msg": "採樣率太低，無法辨識。"
}
2024-01-16 16:07:15.651 [DEBU] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:07:15.652 [DEBU] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=16000",
	"uid": "a8ee066e62c5aa177d5c761f60aa75cd"
}
2024-01-16 16:07:15.714 [DEBU] {a8ee066e62c5aa177d5c761f60aa75cd} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": -2,
	"err_msg": "採樣率太低，無法辨識。"
}
2024-01-16 16:09:29.590 [DEBU] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:09:29.592 [DEBU] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=16000",
	"uid": "80872a9c81c5aa177f5c761f2230dc45"
}
2024-01-16 16:09:30.585 [DEBU] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-172
	],
	"recog_nbest": [
		"這是一段七的語音測試歡迎來到台北市"
	],
	"recog_result": "這是一段七的語音測試歡迎來到台北市",
	"recog_time": 855,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-01-16 16:09:29.972314"
}
2024-01-16 16:09:30.680 [DEBU] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:09:30.681 [DEBU] {80872a9c81c5aa177f5c761f2230dc45} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:629: End of document recognition...
2024-01-16 16:10:14.660 [DEBU] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/cyberon.(*STT).connectHost] cyberon_stt.go:96: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:10:14.661 [DEBU] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile] cyberon_stt.go:556: Request:{
	"action": "start",
	"bIsDoEPD": true,
	"domain": "freeSTT-zh-TW",
	"nBestNum": 1,
	"platform": "web",
	"token": "None",
	"type": "audio/wav;rate=16000",
	"uid": "5801ff1a8cc5aa17805c761febf148b0"
}
2024-01-16 16:10:15.726 [DEBU] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"isFinish": true,
	"path_score": [
		-172
	],
	"recog_nbest": [
		"這是一段七的語音測試歡迎來到台北市"
	],
	"recog_result": "這是一段七的語音測試歡迎來到台北市",
	"recog_time": 950,
	"result_index": 0,
	"state": "result",
	"word_start_time": "2024-01-16 16:10:15.035868"
}
2024-01-16 16:10:15.831 [DEBU] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:627: {
	"err_code": 0,
	"state": "listening"
}
2024-01-16 16:10:15.839 [DEBU] {5801ff1a8cc5aa17805c761febf148b0} [SonaMesh/internal/logic/cyberon.(*STT).RecognizeFile.func1] cyberon_stt.go:629: End of document recognition...
2024-01-16 16:40:26.192 [DEBU] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/cyberon.(*TTS).Synthesis] cyberon_tts.go:149: Request: {
	"gain": 1,
	"language": "zh-TW",
	"outfmt": "wav",
	"phrbrk": false,
	"serviceName": "e2e",
	"speaker": "Sharon",
	"speed": 1,
	"text": " 这是一段tts的测试文本",
	"token": "None",
	"uid": "",
	"vbr_quality": 4
} 
