2024-12-02 15:14:58.113 [DEBU] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/service.GetAzureEndPointKeyAndRegion] nodes.go:74: The key : vendor.Azure_111111_000.STT is not found 
2024-12-02 15:14:58.114 [DEBU] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/service.GetAzureEndPointKeyAndRegion] nodes.go:74: The key : vendor.Azure_111111_000.STT is not found 
2024-12-02 15:14:58.113 [DEBU] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/service.GetAzureEndPointKeyAndRegion] nodes.go:74: The key : vendor.Azure_111111_000.STT is not found 
2024-12-02 15:14:58.114 [DEBU] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:14:58.114 [DEBU] {489dbd29d24a0d184703b4670458df9f} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:63356: use of closed network connection
2024-12-02 15:14:58.115 [DEBU] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:14:58.115 [DEBU] {28ccbd29d24a0d184903b467df363ea8} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:63354: use of closed network connection
2024-12-02 15:14:58.115 [DEBU] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:14:58.115 [DEBU] {30a1bd29d24a0d184803b467a7188b22} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:63355: use of closed network connection
2024-12-02 15:19:23.301 [DEBU] {90aaf02c004b0d181231c5710edb756d} [SonaMesh/internal/service.GetAzureEndPointKeyAndRegion] nodes.go:74: The key : vendor.Azure_111111_000.STT is not found 
2024-12-02 15:20:39.779 [DEBU] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> japaneast , key ->[3d50b4e876194ef89ad6607822426137]
2024-12-02 15:20:39.789 [DEBU] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:20:44.837 [DEBU] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:20:53.476 [DEBU] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:21:02.371 [DEBU] {a843b2b6214b0d18c48a7133b4cb4f89} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:21:15.544 [DEBU] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:21:15.546 [DEBU] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:21:20.600 [DEBU] {90dd6f0a2a4b0d184303ed3bf558075c} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:24:07.701 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:24:07.711 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:24:18.838 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:24:29.072 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:24:31.686 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> japaneast , key ->[3d50b4e876194ef89ad6607822426137]
2024-12-02 15:24:31.687 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:24:36.726 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:24:45.367 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:24:47.144 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:24:49.641 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:150: Trigger on finished function ... 
2024-12-02 15:24:49.641 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:178: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:24:49.642 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:154: Positive to stop...
2024-12-02 15:24:50.032 [DEBU] {c027c71f524b0d18559df5479b837ede} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢，你要登入那個融。"
	},
	"status": 0
}
2024-12-02 15:25:15.642 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:150: Trigger on finished function ... 
2024-12-02 15:25:15.642 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:178: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:25:15.643 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:154: Positive to stop...
2024-12-02 15:25:15.851 [DEBU] {f80075b5574b0d18569df547c1dba7df} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個，我想問說他，我剛好。"
	},
	"status": 0
}
2024-12-02 15:25:33.489 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:25:33.489 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:25:33.489 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> japaneast , key ->[3d50b4e876194ef89ad6607822426137]
2024-12-02 15:25:33.491 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:33.491 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:33.491 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:25:39.537 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:25:44.574 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:25:44.574 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:25:47.646 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:25:58.543 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:25:58.543 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:26:01.535 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:26:01.544 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:26:05.328 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:26:08.318 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:26:08.318 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:26:26.646 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是差一天，為什麼你們門市就不肯讓我折抵啊？而且我這邊還買很多杯，買十幾杯。嗯，小姐，不好意思，因為這不是差差幾天，而是因為他就是過期門市，就沒辦法收券了。"
	},
	"status": 0
}
2024-12-02 15:26:34.346 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:26:34.588 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:26:42.818 [DEBU] {c8611319664b0d18599df54727d2f5a7} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:26:42.818 [DEBU] {f0071319664b0d18589df547b2d55525} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:26:42.818 [DEBU] {00b21219664b0d18579df5475d4a4245} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:28:22.877 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastus , key ->[909e6ef96525409ca8a4158903e7e109]
2024-12-02 15:28:22.877 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastus , key ->[909e6ef96525409ca8a4158903e7e109]
2024-12-02 15:28:22.877 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastus , key ->[909e6ef96525409ca8a4158903e7e109]
2024-12-02 15:28:22.881 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:22.881 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:22.881 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:28:28.672 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:28:29.126 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:28:34.133 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:28:36.749 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:28:37.605 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:28:48.114 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:28:51.041 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:28:51.634 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:28:54.877 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:28:57.895 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:29:16.236 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那可是差一天，為什麼你們門市就不肯讓我折抵啊？而且我這邊還買很多杯，買十幾杯。嗯，小姐，不好意思，因為這不是差差幾天，而是因為他就是過期門市，就沒辦法收券了。"
	},
	"status": 0
}
2024-12-02 15:29:20.189 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:29:23.659 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:29:23.849 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:29:33.399 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:29:34.155 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "啊，我有點忘記了抱歉，因為中間我我我今年去住了兩次院，有點記憶力，有點不太好，對，OK，所以你就是收到那個通知。"
	},
	"status": 0
}
2024-12-02 15:29:34.352 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那這有沒有什麼方式可以這個補我們啦，因為這個很多張欸，我有很多張欸，不是一張而已欸，我了解，但是因為它是一整年度的兌換，所以這個卷的部分它沒有辦法再補位。"
	},
	"status": 0
}
2024-12-02 15:29:48.487 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我，我一直奇怪啊，這是什麼東西我一直想不起來啊。你是哪一些銀行生貸款的？呃，臺灣銀行啊分行呢呃中心分行。"
	},
	"status": 0
}
2024-12-02 15:29:49.307 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那有沒有什麼方式可以理補我們的這種損失啊？因為那個很貴耶小姐，不好意思，因為這個券現在這個年曆的附加贈品，所以它的確沒有辦法，只要只要過期了，就沒辦法再做使用了。"
	},
	"status": 0
}
2024-12-02 15:29:56.704 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "臺銀的中心中心新村分行嗎？對對對，中興新村好，所以你忘記拿收貨袋，反正你就是收到那個平臺。"
	},
	"status": 0
}
2024-12-02 15:29:58.682 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "差一天欸，對不好意思，這個真的沒辦法使用，可以幫我跟你們總公司在噴印嗎？"
	},
	"status": 0
}
2024-12-02 15:30:03.853 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:150: Trigger on finished function ... 
2024-12-02 15:30:03.853 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:178: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:30:03.853 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:154: Positive to stop...
2024-12-02 15:30:04.473 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:30:05.050 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
2024-12-02 15:30:05.185 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:30:05.185 [DEBU] {406f7d898d4b0d1811f4a469facc8d51} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:64790: use of closed network connection
2024-12-02 15:30:12.885 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "嗯，小姐，這個部分我們是確定他，因為往年就算就包含我們今年度的也是一樣，都是1月1號啟用到12月30要把它使用完畢，所以過期了，它確實就沒有辦法再做使用了。"
	},
	"status": 0
}
2024-12-02 15:30:22.818 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "就是通知的簡訊，那先生，那我就是請你要到平台去登入這個資料啊。他說，5月31號填要登入啊，你現在如果說可以的話，你就現在登錄，因為你會忘我用好你，這樣叫我用好不好你，你就是照那個平臺裡面之後啊，那手機點就可以了，是不是如果你手機可以用的話哦，可以來我來，我現在我現在我來。"
	},
	"status": 0
}
2024-12-02 15:30:28.896 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "可是你們這樣很很就是有點不合乎情理，就是說一般是31號，只有你們寫的是30號，誰會知道是這是差這個一天啊，為什麼你們要這樣子訂呢？"
	},
	"status": 0
}
2024-12-02 15:30:29.549 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "然後我第一次登入是按第一次還是我們對對第一次登入對好了，那輸入你的桶編。"
	},
	"status": 0
}
2024-12-02 15:30:35.217 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "然後他會傳驗證碼到你的手機。"
	},
	"status": 0
}
2024-12-02 15:30:41.653 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我覺得這樣你要補給我們消費者才對呀。小姐，不好意思欸，這個真的沒有辦法啦。這個真的不好意思，因為它是整年度的兌換。"
	},
	"status": 0
}
2024-12-02 15:30:46.076 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "他，他是整年度的喜歡不好意思。"
	},
	"status": 0
}
2024-12-02 15:30:46.625 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好傳一次馬到我手機，你已經登錄過了，基本上前往你登入頁面好，那你那你就是回到我已經登入過那個案件。"
	},
	"status": 0
}
2024-12-02 15:30:49.886 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "可以再幫我fine。"
	},
	"status": 0
}
2024-12-02 15:30:51.540 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "欸，他現在跳說。"
	},
	"status": 0
}
2024-12-02 15:30:54.241 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哼嗯。"
	},
	"status": 0
}
2024-12-02 15:30:57.500 [DEBU] {50967d898d4b0d1812f4a469da51c945} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "這個我真的可以跟小姐說明，我們沒有辦法再補，或是就是提供特殊處理啦。這個真的不好意思，因為它已經過期了。"
	},
	"status": 0
}
2024-12-02 15:31:03.384 [DEBU] {184c7d898d4b0d1810f4a469ebbf89c5} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "大家要我輸入那個呢？同邊跟密碼咧啊密碼你記得嗎？我希望看好不好，嗯。"
	},
	"status": 0
}
2024-12-02 15:36:45.806 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastus , key ->[909e6ef96525409ca8a4158903e7e109]
2024-12-02 15:36:45.806 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:36:45.806 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> japaneast , key ->[3d50b4e876194ef89ad6607822426137]
2024-12-02 15:36:45.816 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:45.816 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:45.816 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:36:50.865 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:36:51.008 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:36:56.887 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:36:59.493 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:36:59.626 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:37:10.882 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:37:13.845 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:37:13.847 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:37:13.914 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:37:20.620 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
2024-12-02 15:37:43.028 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:37:43.070 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我跟那個邱小姐稍微解釋一下，就是說清倉貸款吶，本身這個方案是沒有請期限的限制啦，那只是說，因為從去年開始輕裝貸款，如果你有貨貸成功貸款100萬以下的，有那個最長5年的利息補貼嘛，那如果是這個補貼的方案的話，它是指要在3月底之前送件的案件。"
	},
	"status": 0
}
2024-12-02 15:37:46.401 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:37:46.550 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "才有這個資格去申請這個利息的補貼。"
	},
	"status": 0
}
2024-12-02 15:37:46.613 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "3432429834323432我我有點忘記這是什麼東西了，就是你創貸款啊，就是貸款100萬那個對啊，貸款嗎？你公司是房柔有限公司，嗎嘿，對對你是負責人本人嘛，對，那我可以看你哪收貨在青創的嗎？獲袋。"
	},
	"status": 0
}
2024-12-02 15:37:56.470 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:37:56.756 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "好，所以你超過3月底才申請新送貸款的話，差別就是沒有那個100萬5年的利息補貼。"
	},
	"status": 0
}
2024-12-02 15:37:56.901 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "啊，我有點忘記了抱歉，因為中間我我我今年去住了兩次院，有點記憶力，有點不太好，對，OK，所以你就是收到那個通知。"
	},
	"status": 0
}
2024-12-02 15:38:11.228 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "我，我一直奇怪啊，這是什麼東西我一直想不起來啊。你是哪一些銀行生貸款的？呃，臺灣銀行啊分行呢呃中心分行。"
	},
	"status": 0
}
2024-12-02 15:38:19.428 [DEBU] {387e65a2024c0d18f56fa90b85edee09} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "臺銀的中心中心新村分行嗎？對對對，中興新村好，所以你忘記拿收貨袋，反正你就是收到那個平臺。"
	},
	"status": 0
}
2024-12-02 15:38:26.774 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:150: Trigger on finished function ... 
2024-12-02 15:38:26.774 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:178: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:38:26.775 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:154: Positive to stop...
2024-12-02 15:38:26.775 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:150: Trigger on finished function ... 
2024-12-02 15:38:26.775 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func4] websocket_man.go:178: Recognition finished:{
	"ack": "finish",
	"message": {
		"type": "ack"
	},
	"status": -6
}
2024-12-02 15:38:26.775 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/stt.(*sSTT).ackHandler] stt.go:154: Positive to stop...
2024-12-02 15:38:27.306 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:38:27.312 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "哦，那如果補貼的話是就是申請下來，第一年開始就是要還還款這樣嗎？還是沒有啊。本來本來新莊貸款本來就是一開始，你要先本金加利息做繳款啊，對，因為我們利息補貼的方式還是會還是他的方式，還是說你第一個月貨帶下來，你還是先繳先繳先繳本金跟利息的費用，那到時候銀行端去申請到補貼，他才會再把補貼。"
	},
	"status": 0
}
2024-12-02 15:38:27.420 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
2024-12-02 15:38:27.447 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:38:27.447 [DEBU] {c00e66a2024c0d18f76fa90bf5db9bee} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:49544: use of closed network connection
2024-12-02 15:38:27.567 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "的那個。"
	},
	"status": 0
}
2024-12-02 15:38:27.644 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:84: final close connection
2024-12-02 15:38:27.644 [DEBU] {208265a2024c0d18f66fa90b642e457a} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func1] websocket_man.go:87: close tcp [::1]:8000->[::1]:49542: use of closed network connection
2024-12-02 15:40:56.792 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastus , key ->[909e6ef96525409ca8a4158903e7e109]
2024-12-02 15:40:56.793 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> japaneast , key ->[3d50b4e876194ef89ad6607822426137]
2024-12-02 15:40:56.793 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/azure.(*STT).Connect] azure_stt.go:84: Select region -> eastasia , key ->[f8506cbed78841ab9396c40a88c97f5f]
2024-12-02 15:40:56.803 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:40:56.803 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:40:56.803 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/stt.(*sSTT).Start] stt.go:221: Start Ack: {
	"ack": "start",
	"message": {
		"type": "ack"
	},
	"status": 0
}
2024-12-02 15:41:01.884 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "3d50b4e876194ef89ad6607822426137",
	"region": "japaneast",
	"weight": 95
}
2024-12-02 15:41:01.886 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "馬三曼福中心，您好，畢竟您高興為您服務。"
	},
	"status": 0
}
2024-12-02 15:41:02.998 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "909e6ef96525409ca8a4158903e7e109",
	"region": "eastus",
	"weight": 94
}
2024-12-02 15:41:02.998 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "星巴克，你好。"
	},
	"status": 0
}
2024-12-02 15:41:07.896 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "f8506cbed78841ab9396c40a88c97f5f",
	"region": "eastasia",
	"weight": 89
}
2024-12-02 15:41:07.897 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "Hey，how hey miyaho欸，我一直收到你們這個簡訊是什麼意思？哈登入平臺嗎？對。"
	},
	"status": 0
}
2024-12-02 15:41:10.580 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "3d50b4e876194ef89ad6607822426137",
	"region": "japaneast",
	"weight": 89
}
2024-12-02 15:41:10.580 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喂，你好，那個如果是有那個創業貸款是問問這邊嗎？對這邊可以幫您做說明。"
	},
	"status": 0
}
2024-12-02 15:41:11.107 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "909e6ef96525409ca8a4158903e7e109",
	"region": "eastus",
	"weight": 87
}
2024-12-02 15:41:11.108 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "小姐，我想要反應一下，我買那個整本的那個筆記本，後面有咖啡卷嘛是。"
	},
	"status": 0
}
2024-12-02 15:41:21.843 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "f8506cbed78841ab9396c40a88c97f5f",
	"region": "eastasia",
	"weight": 77
}
2024-12-02 15:41:21.843 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "青年創業及啟動金融金代榮經代款榮對呀，這個就是你，你不是貨，嘿對，然後呢？你要登入那個融資平臺去登入。"
	},
	"status": 0
}
2024-12-02 15:41:24.812 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "3d50b4e876194ef89ad6607822426137",
	"region": "japaneast",
	"weight": 80
}
2024-12-02 15:41:24.812 [DEBU] {88d658123d4c0d18a9151e75691ff748} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "喔那個我想問說他，我看網路上寫申請到3月31，後面就不能申請了，嗎哦跟小姐請教一下怎麼稱呼呢？火星球好，邱小姐您好，您是從哪個縣市來電的呢？"
	},
	"status": 0
}
2024-12-02 15:41:24.852 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "f8506cbed78841ab9396c40a88c97f5f",
	"region": "eastasia",
	"weight": 68
}
2024-12-02 15:41:24.852 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "您公司的前一年度的營業額跟員工人數啊。"
	},
	"status": 0
}
2024-12-02 15:41:28.766 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "909e6ef96525409ca8a4158903e7e109",
	"region": "eastus",
	"weight": 77
}
2024-12-02 15:41:28.766 [DEBU] {88d658123d4c0d18a8151e7533532d52} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "那你們那個日期啊，日月這麼小，我以為是到12月31號，結果結果是到12月30號欸，對，因為我們往年每一年的券，它都是到12月30號，所以卷的使用期限確實是到12月30。"
	},
	"status": 0
}
2024-12-02 15:41:31.629 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/service.SetNodeCost] nodes.go:61: {
	"key": "f8506cbed78841ab9396c40a88c97f5f",
	"region": "eastasia",
	"weight": 60
}
2024-12-02 15:41:31.629 [DEBU] {a82459123d4c0d18aa151e75f77a9779} [SonaMesh/internal/logic/websocket_man.(*sWebSocketMan)._processor.func3] websocket_man.go:159: Recognition result:{
	"final": true,
	"message": {
		"type": "result"
	},
	"result": {
		"likelihood": 1,
		"transcript": "429八。"
	},
	"status": 0
}
