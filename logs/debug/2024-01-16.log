2024-01-16 16:07:45.874 [DEBU] {a07cb58269c5aa177e5c761faa333299} [SonaMesh/utility.Is16BitWav] helper.go:88: The WAV file [/var/folders/fr/2j__trnx40b4n4mb9tqp3nsm0000gn/T/sonamesh/Speech Studio Test.mp3] is not 16-bit little-endian.
2024-01-16 16:36:17.321 [DEBU] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:36:17.323 [DEBU] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:36:19.010 [DEBU] {88a90afcf7c6aa1773bf1679060ece9a} [SonaMesh/utility.WriteToFile] helper.go:33: Write to voice file with content is  这是一段tts的测试文本 
2024-01-16 16:36:40.011 [DEBU] {28361245fdc6aa1774bf16798c000488} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:36:40.011 [DEBU] {28361245fdc6aa1774bf16798c000488} [SonaMesh/internal/logic/tts.(*sTts).Synthesis] tts.go:319: Synthesis find text from cache , file is :./voc/2024-01-16/1705394179010309.wav
2024-01-16 16:36:56.745 [DEBU] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:36:56.745 [DEBU] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:36:57.700 [DEBU] {d0f70e2b01c7aa17c6ac0d59dc280614} [SonaMesh/utility.WriteToFile] helper.go:33: Write to voice file with content is  这是一段tts的测试文本 
2024-01-16 16:37:21.049 [DEBU] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:37:21.049 [DEBU] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:37:21.684 [DEBU] {084fb3d306c7aa17d9466631e4d854d2} [SonaMesh/utility.WriteToFile] helper.go:33: Write to voice file with content is  这是一段tts的测试文本 
2024-01-16 16:40:26.187 [DEBU] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:40:26.187 [DEBU] {30b453ee31c7aa17acfa5761d1fb07a9} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:49:13.192 [DEBU] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:49:13.194 [DEBU] {c0f751a1acc7aa17f1f5fb20fa8131e3} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:49:31.494 [DEBU] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:49:31.494 [DEBU] {d88897e5b0c7aa175eeeb831358e1edf} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:49:45.572 [DEBU] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:49:45.573 [DEBU] {a036a02cb4c7aa175feeb8316bf1866a} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:49:56.618 [DEBU] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:49:56.619 [DEBU] {e8f11bbfb6c7aa177967f450acb6dd07} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:50:29.151 [DEBU] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:50:29.151 [DEBU] {308b3652bec7aa17cce1e2709cb5684b} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
2024-01-16 16:51:01.559 [DEBU] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:224: Find file from cache, Text: 这是一段tts的测试文本
2024-01-16 16:51:01.559 [DEBU] {6088eeddc5c7aa17012216537b826b81} [SonaMesh/internal/logic/tts.(*sTts).findFromCache] tts.go:244: The text not in cache...
