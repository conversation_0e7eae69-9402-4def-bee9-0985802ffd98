# SonaMesh API 規格開發文件

## 項目概述

SonaMesh 是一個專為 IPPBX 系統設計的語音服務中間代理程式，提供語音轉文字（STT）和文字轉語音（TTS）服務的統一接口。本系統整合了 Microsoft Azure Cognitive Services，為企業級通信系統提供高品質的語音處理能力。

### 核心特性

- **多租戶支援**：透過 `vccid` 實現多租戶隔離
- **路由管理**：支援 `route_access_code` 進行服務路由
- **實時處理**：提供 WebSocket 即時語音轉文字功能
- **多格式支援**：支援 WAV、MP3、PCM 等音頻格式
- **多語言支援**：支援繁體中文、簡體中文等多種語言
- **高可用性**：基於 GoFrame 框架，支援容器化部署

### 技術架構

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   IPPBX 系統    │───▶│   SonaMesh      │───▶│  Azure 語音服務  │
│                 │    │   代理服務       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Redis 緩存    │
                       └─────────────────┘
```

## 基礎資訊

### 服務端點
- **基礎 URL**: `http://your-domain:8000` 或 `http://your-domain:8001`
- **API 版本**: v1
- **協議**: HTTP/HTTPS, WebSocket

### 認證機制
本系統使用多層認證機制：
1. **租戶識別**: `vccid` (必填)
2. **路由代碼**: `route_access_code` (選填)
3. **資源金鑰**: `resource_key` (選填)

### 通用請求頭
```http
Content-Type: application/json
Accept: application/json
```

### 通用響應格式
```json
{
  "code": 0,
  "message": "success",
  "result": "響應數據"
}
```

### 狀態碼說明
- `0`: 成功
- `100`: 處理中
- `102`: 完成
- `400`: 請求參數錯誤
- `401`: 認證失敗
- `500`: 服務器內部錯誤

## API 接口規格

### 1. 語音轉文字 (STT) API

#### 1.1 文件上傳轉換
**端點**: `POST /v1/stt/file`

**描述**: 上傳音頻文件進行語音轉文字處理

**請求格式**: `multipart/form-data`

**請求參數**:
| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | ✓ | 音頻文件 (支援 WAV, MP3, PCM) |
| vccid | String | ✓ | 租戶唯一識別碼 |
| route_access_code | String | ✗ | 路由存取代碼 |
| web_hook | String | ✗ | 回調 URL |
| is_wait_result | Boolean | ✗ | 是否等待結果 |
| resource_key | String | ✗ | Azure 資源金鑰 |
| end_point_id | String | ✗ | Azure 端點 ID |
| region | String | ✗ | Azure 區域 |
| translate | Boolean | ✗ | 是否啟用翻譯模式 |

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "result": "轉換後的文字內容"
}
```

**使用範例**:
```bash
curl -X POST http://your-domain:8000/v1/stt/file \
  -F "file=@audio.wav" \
  -F "vccid=111111" \
  -F "route_access_code=000" \
  -F "is_wait_result=true"
```

#### 1.2 WebSocket 即時轉換
**端點**: `WS /ws/stt`

**描述**: 透過 WebSocket 進行即時語音轉文字處理

**連接範例**:
```javascript
const ws = new WebSocket('ws://your-domain:8000/ws/stt');
```

**訊息格式**:

**開始轉換**:
```json
{
  "action": "start",
  "call_id": "unique_call_id",
  "vccid": "111111",
  "route_access_code": "000",
  "enable_partial": false,
  "short_command": false
}
```

**發送音頻數據**: 
- 格式: 二進制數據
- 採樣率: 16kHz
- 位深度: 16-bit
- 聲道: 單聲道

**接收響應**:

**確認訊息**:
```json
{
  "ack": "start",
  "status": 0,
  "message": {
    "type": "ack"
  }
}
```

**識別結果**:
```json
{
  "final": true,
  "status": 0,
  "message": {
    "type": "result"
  },
  "result": {
    "transcript": "識別出的文字",
    "likelihood": 1.0
  }
}
```

**結束轉換**:
```json
{
  "action": "stop"
}
```

### 2. 文字轉語音 (TTS) API

#### 2.1 語音合成
**端點**: `POST /v1/tts/`

**描述**: 將文字轉換為語音文件

**請求參數**:
| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| text | String | ✓ | 要合成的文字內容 |
| vccid | String | ✓ | 租戶唯一識別碼 |
| language | String | ✗ | 語言代碼 (如: zh-TW, zh-CN) |
| type | String | ✗ | 音頻格式 (wav, mp3, pcm) |
| speed | Float | ✗ | 語速 (0.5-2.0) |
| volume | Float | ✗ | 音量 (0-100) |
| user_id | String | ✗ | 用戶唯一識別碼 |
| route_access_code | String | ✗ | 路由存取代碼 |
| text_format | String | ✗ | 文字格式 (預設: text) |

**請求範例**:
```json
{
  "text": "您好，歡迎使用 SonaMesh 語音服務",
  "vccid": "111111",
  "language": "zh-TW",
  "type": "wav",
  "speed": 1.0,
  "volume": 50.0,
  "route_access_code": "000"
}
```

**響應**: 直接返回音頻文件流

**使用範例**:
```bash
curl -X POST http://your-domain:8000/v1/tts/ \
  -H "Content-Type: application/json" \
  -d '{
    "text": "您好，歡迎使用語音服務",
    "vccid": "111111",
    "language": "zh-TW",
    "type": "wav"
  }' \
  --output output.wav
```

#### 2.2 語音文件下載
**端點**: `GET /v1/tts/download_voice_file`

**描述**: 下載已生成的語音文件

**請求參數**:
| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| file_name | String | ✓ | 文件名稱 |

**使用範例**:
```bash
curl -X GET "http://your-domain:8000/v1/tts/download_voice_file?file_name=audio_123.wav"
```

## 錯誤處理

### 常見錯誤碼
| 錯誤碼 | 描述 | 解決方案 |
|--------|------|----------|
| 400 | 請求參數錯誤 | 檢查必填參數和參數格式 |
| 401 | 認證失敗 | 檢查 vccid 和 route_access_code |
| 404 | 資源不存在 | 檢查 API 端點路徑 |
| 413 | 文件過大 | 減小音頻文件大小 |
| 415 | 不支援的媒體類型 | 使用支援的音頻格式 |
| 500 | 服務器內部錯誤 | 聯繫技術支援 |

### 錯誤響應格式
```json
{
  "code": 400,
  "message": "參數錯誤：缺少必填參數 vccid",
  "result": null
}
```

## 最佳實踐

### 1. 音頻文件要求
- **格式**: WAV (推薦), MP3, PCM
- **採樣率**: 16kHz (推薦), 8kHz
- **位深度**: 16-bit
- **聲道**: 單聲道 (推薦)
- **文件大小**: 建議不超過 10MB

### 2. WebSocket 使用建議
- 建立連接後先發送 start 訊息
- 音頻數據建議每次發送 6400 bytes
- 及時處理 ack 和 result 訊息
- 適當處理連接斷開和重連

### 3. 效能優化
- 使用適當的音頻壓縮格式
- 合理設置緩存策略
- 監控 API 調用頻率
- 實施適當的重試機制

### 4. 安全考量
- 妥善保管 vccid 和 route_access_code
- 使用 HTTPS 進行生產環境部署
- 實施 API 調用頻率限制
- 定期輪換認證憑證

## 整合範例

### JavaScript/Node.js 整合

**STT WebSocket 客戶端**:
```javascript
class SonaMeshSTTClient {
  constructor(wsUrl, config) {
    this.wsUrl = wsUrl;
    this.config = config;
    this.ws = null;
  }

  connect() {
    this.ws = new WebSocket(this.wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket 連接已建立');
      this.startRecognition();
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket 錯誤:', error);
    };
  }

  startRecognition() {
    const startMessage = {
      action: "start",
      call_id: this.generateCallId(),
      vccid: this.config.vccid,
      route_access_code: this.config.routeAccessCode,
      enable_partial: false,
      short_command: false
    };

    this.ws.send(JSON.stringify(startMessage));
  }

  sendAudioData(audioBuffer) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(audioBuffer);
    }
  }

  handleMessage(data) {
    switch(data.message?.type) {
      case 'ack':
        console.log('收到確認:', data.ack);
        break;
      case 'result':
        console.log('識別結果:', data.result.transcript);
        break;
    }
  }

  generateCallId() {
    return 'call_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// 使用範例
const client = new SonaMeshSTTClient('ws://your-domain:8000/v1/stt/ws', {
  vccid: '111111',
  routeAccessCode: '000'
});

client.connect();
```

**TTS HTTP 客戶端**:
```javascript
async function synthesizeText(text, options = {}) {
  const response = await fetch('http://your-domain:8000/v1/tts/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      text: text,
      vccid: options.vccid || '111111',
      language: options.language || 'zh-TW',
      type: options.type || 'wav',
      speed: options.speed || 1.0,
      volume: options.volume || 50.0,
      route_access_code: options.routeAccessCode || '000'
    })
  });

  if (response.ok) {
    const audioBlob = await response.blob();
    return audioBlob;
  } else {
    throw new Error(`TTS 請求失敗: ${response.status}`);
  }
}

// 使用範例
synthesizeText('您好，歡迎使用 SonaMesh 語音服務', {
  language: 'zh-TW',
  speed: 1.2,
  volume: 60
}).then(audioBlob => {
  const audioUrl = URL.createObjectURL(audioBlob);
  const audio = new Audio(audioUrl);
  audio.play();
}).catch(error => {
  console.error('語音合成失敗:', error);
});
```

## 技術支援

### 聯繫資訊
- **技術文檔**: [項目 README](../README.MD)
- **問題回報**: 請透過項目 Issue 系統提交
- **版本資訊**: 請查看項目 Release 頁面

### 更新日誌
- **v1.0.2**: 當前版本，支援 Azure 語音服務整合
- 詳細更新記錄請參考項目版本標籤

### 社群支援
- **開發者論壇**: 技術討論和經驗分享
- **文檔貢獻**: 歡迎提交文檔改進建議
- **功能請求**: 透過 Issue 系統提交新功能需求

---

*本文檔最後更新時間: 2024-07-30*
*文檔版本: v1.0*
*適用於 SonaMesh v1.0.2+*
