package utility

import (
	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"os"
)

// WriteToFile is a function that writes the content of a byte buffer to a file.
// It takes four input parameters:
//   - ctx: A context.Context for logging and error handling purposes.
//   - text: A string representing the text used to generate the output file name.
//   - outputType: A string representing the type of output (e.g., "wav", "mp3") used to determine the file extension.
//   - buf: A byte slice containing the data to be written to the file.
// The function returns three output parameters:
//   - baseName: A string representing the base name of the generated output file.
//   - ext: A string representing the file extension of the generated output file.
//   - err: An error value indicating if any error occurred during the file writing process.
//
// The function generates a unique file name based on the input text and output type, creates the file,
// and writes the content of the byte buffer to it. If there is an error creating or writing to the file,
// it logs the error and returns the error value.

func WriteToFile(ctx context.Context, text, outputType string, buf []byte) (baseName, ext string, err error) {
	g.Log().Cat(INFO).Infof(ctx, "Write to file file suffix is %s ", outputType)
	g.Log().Cat(DEBUG).Debugf(ctx, "Write to voice file with content is %s ", text)

	bSave, _ := g.Cfg().Get(ctx, CfgKeyTTS+".save_voice_file", false)

	fileName := ""
	if bSave.Bool() {
		//When the file name is parsed using timestamp, the corresponding folder can be found.
		fileName = fmt.Sprintf("%s.%s", gtime.TimestampMicroStr(), TypeSuffix[outputType])
	} else {

		return "", "", nil
	}

	sFolder, _ := g.Cfg().Get(ctx, CfgKeyTTS+".save_folder", "./voc")
	fullName := gfile.Join(sFolder.String(), gtime.Now().Format("Y-m-d"), fileName)

	if err = gfile.PutBytes(fullName, buf); err != nil {
		g.Log().Cat(DEBUG).Error(ctx, err)
		return
	}

	baseName = gfile.Basename(fullName)
	ext = gfile.ExtName(fullName)
	_ = gcache.Set(ctx, text, fullName, gcache.DurationNoExpire)

	return

}

// Is16BitWav is a function that checks if a given WAV file is in 16-bit little-endian PCM format.
// It takes two input parameters:
//   - ctx: A context.Context for logging and error handling purposes.
//   - fullFileName: A string representing the full path of the WAV file to be checked.
// The function returns a boolean value indicating whether the file is in the desired format.
//
// The function opens the file and reads its header. If there is an error opening the file or
// reading the header, it logs the error and returns false.
//
// If the audio format is not 1 (PCM) or the bits per sample is not 16, it logs a debug message
// and returns false. Otherwise, it returns true.

func Is16BitWav(ctx context.Context, fullFileName string) bool {
	file, err := os.Open(fullFileName)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		return false
	}
	defer file.Close()
	var header model.WavHeader
	err = binary.Read(file, binary.LittleEndian, &header)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		return false
	}
	if header.AudioFormat != 1 || header.BitsPerSample != 16 {
		g.Log().Cat(DEBUG).Debugf(ctx, "The WAV file [%s] is not 16-bit little-endian.", fullFileName)
		return false
	} else {
		return true
	}

}

type WAVHeader struct {
	ChunkID       [4]byte // "RIFF"
	ChunkSize     uint32  // 文件大小 - 8
	Format        [4]byte // "WAVE"
	Subchunk1ID   [4]byte // "fmt "
	Subchunk1Size uint32  // 16 (PCM格式)
	AudioFormat   uint16  // 1 (PCM)
	NumChannels   uint16  // 声道数 (1=单声道, 2=立体声)
	SampleRate    uint32  // 采样率 (16000)
	ByteRate      uint32  // 每秒字节数
	BlockAlign    uint16  // 数据块对齐
	BitsPerSample uint16  // 位深度 (16)
	Subchunk2ID   [4]byte // "data"
	Subchunk2Size uint32  // 音频数据大小
}

// AddWAVHeader 给原始音频数据添加WAV头
func AddWAVHeader(rawData []byte, sampleRate uint32, channels uint16, bitsPerSample uint16) []byte {
	header := WAVHeader{
		ChunkID:       [4]byte{'R', 'I', 'F', 'F'},
		Format:        [4]byte{'W', 'A', 'V', 'E'},
		Subchunk1ID:   [4]byte{'f', 'm', 't', ' '},
		Subchunk1Size: 16,
		AudioFormat:   1, // PCM
		NumChannels:   channels,
		SampleRate:    sampleRate,
		BitsPerSample: bitsPerSample,
		Subchunk2ID:   [4]byte{'d', 'a', 't', 'a'},
		Subchunk2Size: uint32(len(rawData)),
	}

	// 计算相关字段
	header.ByteRate = sampleRate * uint32(channels) * uint32(bitsPerSample) / 8
	header.BlockAlign = channels * bitsPerSample / 8
	header.ChunkSize = uint32(len(rawData)) + 36

	// 将header转换为字节
	var buf bytes.Buffer
	binary.Write(&buf, binary.LittleEndian, header)

	// 合并header和音频数据
	result := make([]byte, 0, len(buf.Bytes())+len(rawData))
	result = append(result, buf.Bytes()...)
	result = append(result, rawData...)

	return result
}

func SplitParamsToMap(in string) (out g.Map) {
	out = make(g.Map)
	if g.IsEmpty(gstr.Trim(in)) {
		return
	}
	ary := gstr.SplitAndTrim(in, ";")
	if len(ary) == 0 {
		return
	}
	for _, v := range ary {
		kv := gstr.Split(v, "=")
		if len(kv) == 2 {
			out[kv[0]] = kv[1]
		}
	}

	return
}
